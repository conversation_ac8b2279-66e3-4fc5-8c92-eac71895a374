<script setup lang="ts">
import { computed, ref, watch, onMounted, shallowRef } from 'vue';
import { 
  Form, 
  Input, 
  Select, 
  DatePicker, 
  Switch, 
  Button,
  Row,
  Col,
  Card,
  message,
  Modal
} from 'ant-design-vue';
import dayjs from 'dayjs';

// 导入组件
import RichEditor from './rich-editor.vue';
import ImageUpload from '#/components/image/image-upload.vue';
import WechatCrawlerModal from './wechat-crawler-modal.vue';

// 导入stores
import { useArticleAuthorStore } from '#/store/article/author';
import { useArticleTagStore } from '#/store/article/tag';
import { useWechatCrawlerStore } from '#/store/article/wechat-crawler';

// 导入类型和常量
import type { ArticleFormData } from '#/types';
import type { WechatArticleInfo } from '#/store/article/wechat-crawler';
import {
  ARTICLE_CHANNEL_OPTIONS,
  ARTICLE_STATUS_OPTIONS
} from '#/types';

defineOptions({
  name: 'ArticleForm',
});

const props = defineProps<{
  loading?: boolean;
  isEdit?: boolean;
}>();

const emit = defineEmits<{
  submit: [formData: ArticleFormData];
  cancel: [];
}>();

// 使用stores
const authorStore = useArticleAuthorStore();
const tagStore = useArticleTagStore();
const crawlerStore = useWechatCrawlerStore();

// 表单引用
const formRef = ref();

// 微信爬取弹窗状态
const showWechatCrawler = ref(false);

// 测试HTML内容弹窗状态
const showTestHtmlModal = ref(false);
const testHtmlContent = ref('');

// 获取HTML内容弹窗状态
const showCurrentHtmlModal = ref(false);
const currentHtmlContent = ref('');

// 表单数据
const formData = ref<ArticleFormData>({
  title: '',
  style: '富文本',
  content: '',
  channel: '头条',
  cover_image: '',
  summary: '',
  share_image: '',
  publish_time: dayjs().toISOString(),
  is_visible: true,
  seo_title: '',
  seo_keywords: '',
  seo_description: '',
  status: '1', // 默认待审核
  author_id: 0,
  tag_ids: [],
});

// 作者选项
const authorOptions = ref<Array<{ label: string; value: number }>>([]);

// 标签选项
const tagOptions = ref<Array<{ label: string; value: number }>>([]);

// 发布时间的dayjs对象，用于DatePicker
const publishTimeValue = computed({
  get() {
    return formData.value.publish_time ? dayjs(formData.value.publish_time) : undefined;
  },
  set(value: any) {
    formData.value.publish_time = value ? value.toISOString() : '';
  }
});

// 表单验证规则
const formRules: any = {
  title: [
    { required: true, message: '请输入文章标题', trigger: 'blur' },
    { max: 100, message: '标题不能超过100个字符', trigger: 'blur' },
  ],

  content: [
    { required: true, message: '请输入文章内容', trigger: 'blur' },
  ],
  channel: [
    { required: true, message: '请选择发布频道', trigger: 'change' },
  ],
  publish_time: [
    { required: true, message: '请选择发布时间', trigger: 'change' },
  ],
  status: [
    { required: true, message: '请选择文章状态', trigger: 'change' },
  ],
  author_id: [
    { required: true, message: '请选择文章作者', trigger: 'change' },
  ],
  tag_ids: [
    { required: true, message: '请选择文章标签', trigger: 'change' },
    { type: 'array', min: 1, message: '至少选择一个标签', trigger: 'change' },
  ],
  summary: [
    { max: 300, message: '简介不能超过300个字符', trigger: 'blur' },
  ],
  seo_title: [
    { max: 100, message: 'SEO标题不能超过100个字符', trigger: 'blur' },
  ],
  seo_keywords: [
    { max: 100, message: 'SEO关键字不能超过100个字符', trigger: 'blur' },
  ],
  seo_description: [
    { max: 300, message: 'SEO描述不能超过300个字符', trigger: 'blur' },
  ],
};

// 加载作者列表
async function loadAuthors() {
  try {
    await authorStore.getArticleAuthorList({ page: 1, page_size: 100 });
    authorOptions.value = authorStore.authors.map(author => ({
      label: `${author.author_name} (${author.wechat_nickname})`,
      value: author.id!,
    }));
  } catch (error) {
    message.error('加载作者列表失败');
  }
}

// 加载标签列表
async function loadTags() {
  try {
    await tagStore.getArticleTagList({ page: 1, page_size: 100 });
    tagOptions.value = tagStore.tags.map(tag => ({
      label: tag.name,
      value: tag.id!,
    }));
  } catch (error) {
    message.error('加载标签列表失败');
  }
}

// 处理表单提交
async function handleSubmit() {
  try {
    await formRef.value?.validate();
    
    // 转换发布时间格式
    const submitData = {
      ...formData.value,
      publish_time: dayjs(formData.value.publish_time).toISOString(),
    };
    
    emit('submit', submitData);
  } catch (error) {
    console.error('表单验证失败:', error);
  }
}

// 处理取消
function handleCancel() {
  emit('cancel');
}

// 重置表单
function resetForm() {
  formData.value = {
    title: '',
    style: '富文本',
    content: '',
    channel: '头条',
    cover_image: '',
    summary: '',
    share_image: '',
    publish_time: dayjs().toISOString(),
    is_visible: true,
    seo_title: '',
    seo_keywords: '',
    seo_description: '',
    status: '1',
    author_id: 0,
    tag_ids: [],
  };
  formRef.value?.resetFields();
}

// 设置表单数据（用于编辑模式）
function setFormData(data: ArticleFormData) {
  formData.value = {
    ...data,
    style: '富文本' // 强制设置为富文本格式
  };
  // 更新发布时间的dayjs对象
  publishTimeValue.value = dayjs(data.publish_time);
}

// 显示微信爬取弹窗
function showWechatCrawlerModal() {
  showWechatCrawler.value = true;
}

// 富文本编辑器引用
const richEditorRef = shallowRef();

// 处理微信文章爬取成功
function handleWechatCrawlSuccess(data: WechatArticleInfo) {
  try {
    // 填充表单数据
    if (data.title) {
      formData.value.title = data.title;
    }

    if (data.content) {
      console.log('开始处理微信文章内容，原始内容长度:', data.content.length);
      console.log('原始内容预览:', data.content.substring(0, 200) + '...');
      
      // 直接使用原始HTML内容，不进行清理
      const rawContent = data.content;
      console.log('原始内容长度:', rawContent.length);

      // 使用防抖方式设置内容，确保编辑器已初始化
      const setContentWithRetry = (retryCount = 0) => {
        if (retryCount > 10) {
          console.error('编辑器初始化超时，无法设置内容');
          // 最后的备用方案：直接设置formData
          formData.value.content = rawContent;
          message.warning('编辑器初始化超时，内容已设置到表单，请手动刷新编辑器');
          return;
        }

        // 检查编辑器引用和方法是否存在
        if (!richEditorRef.value) {
          console.log('编辑器引用不存在，等待初始化...');
          setTimeout(() => setContentWithRetry(retryCount + 1), 200);
          return;
        }

        if (!richEditorRef.value.setHtmlContent) {
          console.log('setHtmlContent方法不存在，等待初始化...');
          setTimeout(() => setContentWithRetry(retryCount + 1), 200);
          return;
        }

        try {
          console.log(`第${retryCount + 1}次尝试使用setHtmlContent方法设置原始内容`);
          console.log('准备设置的内容长度:', rawContent.length);
          
          // 直接调用setHtmlContent方法设置原始内容
          // 使用setTimeout确保在下一个事件循环中执行，提高成功率
          setTimeout(() => {
            richEditorRef.value.setHtmlContent(rawContent);
          }, 0);
          
          // 同步更新formData
          formData.value.content = rawContent;
          console.log('内容设置成功');

        } catch (error: any) {
          console.error('setHtmlContent方法执行失败，重试中...', error);
          console.error('错误详情:', {
            errorMessage: error?.message || '未知错误',
            contentLength: rawContent.length,
            contentType: typeof rawContent,
            editorRef: !!richEditorRef.value,
            setHtmlMethod: !!richEditorRef.value?.setHtmlContent
          });
          setTimeout(() => setContentWithRetry(retryCount + 1), 200);
        }
      };

      // 开始设置内容
      setContentWithRetry();
    }

    if (data.summary) {
      formData.value.summary = data.summary;
    } else if (data.content) {
      // 如果没有摘要，从内容中提取
      formData.value.summary = crawlerStore.extractSummary(data.content, 200);
    }

    // 如果有封面图片，设置封面
    if (data.cover_image) {
      formData.value.cover_image = data.cover_image;
    }

    message.success('微信文章内容已成功导入到编辑器！');
  } catch (error) {
    console.error('处理爬取数据失败:', error);
    message.error('导入文章内容时出现错误: ' + (error as Error).message);
  }
}

// 显示测试HTML内容弹窗
function showTestHtmlModalFn() {
  showTestHtmlModal.value = true;
  testHtmlContent.value = '';
}

// 设置测试HTML内容
function setTestHtmlContent() {
  try {
    console.log('设置测试HTML内容，长度:', testHtmlContent.value.length);
    console.log('内容预览:', testHtmlContent.value.substring(0, 200) + '...');
    
    if (richEditorRef.value && richEditorRef.value.setHtmlContent) {
      richEditorRef.value.setHtmlContent(testHtmlContent.value);
      formData.value.content = testHtmlContent.value;
      showTestHtmlModal.value = false;
      message.success('HTML内容已设置');
    } else {
      message.error('编辑器未初始化或缺少setHtmlContent方法');
    }
  } catch (error) {
    console.error('设置HTML内容失败:', error);
    message.error('设置HTML内容失败: ' + (error as Error).message);
  }
}

// 显示当前HTML内容弹窗
function showCurrentHtmlModalFn() {
  try {
    const currentContent = formData.value.content;
    console.log('获取当前HTML内容，长度:', currentContent.length);
    console.log('内容预览:', currentContent.substring(0, 200) + '...');
    
    currentHtmlContent.value = currentContent;
    showCurrentHtmlModal.value = true;
  } catch (error) {
    console.error('获取HTML内容失败:', error);
    message.error('获取HTML内容失败: ' + (error as Error).message);
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadAuthors();
  loadTags();
});

// 暴露方法给父组件
defineExpose({
  resetForm,
  setFormData,
});
</script>

<template>
  <div class="article-form">
    <Form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      layout="vertical"
      :scroll-to-first-error="true"
    >
      <Row :gutter="24">
        <!-- 左侧主要内容 -->
        <Col :span="18">
          <Card title="基本信息" class="mb-6">
            <Form.Item label="文章标题" name="title">
              <Input
                v-model:value="formData.title"
                placeholder="请输入文章标题（最多100个字符）"
                :maxlength="100"
                show-count
              />
            </Form.Item>

            <!-- 文章风格隐藏字段，默认为富文本 -->
            <Form.Item name="style" style="display: none;">
              <Input v-model:value="formData.style" />
            </Form.Item>

            <Form.Item label="发布频道" name="channel">
              <Select
                v-model:value="formData.channel"
                placeholder="请选择发布频道"
                :options="ARTICLE_CHANNEL_OPTIONS"
                style="width: 200px;"
              />
            </Form.Item>

            <Form.Item name="content">
              <template #label>
                <div class="flex items-center justify-between w-full">
                  <span>文章内容</span>
                  <div>
                    <Button
                      type="link"
                      size="small"
                      @click="showWechatCrawlerModal"
                    >
                      📱 微信文章导入
                    </Button>
                    <Button
                      type="link"
                      size="small"
                      @click="showTestHtmlModalFn"
                    >
                      🧪 设置HTML测试
                    </Button>
                    <Button
                      type="link"
                      size="small"
                      @click="showCurrentHtmlModalFn"
                    >
                      📋 获取HTML内容
                    </Button>
                  </div>
                </div>
              </template>
              <RichEditor
                ref="richEditorRef"
                v-model="formData.content"
                placeholder="请输入文章内容..."
                :height="500"
              />
            </Form.Item>

            <Form.Item label="文章简介" name="summary">
              <Input.TextArea
                v-model:value="formData.summary"
                placeholder="请输入文章简介（最多300个字符）"
                :maxlength="300"
                :rows="4"
                show-count
              />
            </Form.Item>
          </Card>

          <!-- 封面设置 - 横向布局 -->
          <Card title="封面设置" class="mb-6">
            <Row :gutter="24">
              <Col :span="12">
                <Form.Item label="文章封面" name="cover_image">
                  <ImageUpload
                    v-model="formData.cover_image"
                    placeholder="上传文章封面"
                    category="article"
                  />
                </Form.Item>
              </Col>
              <Col :span="12">
                <Form.Item label="分享封面" name="share_image">
                  <ImageUpload
                    v-model="formData.share_image"
                    placeholder="上传分享封面"
                    category="article"
                  />
                </Form.Item>
              </Col>
            </Row>
          </Card>
        </Col>

        <!-- 右侧设置 -->
        <Col :span="6">
          <Card title="发布设置" class="mb-6">
            <Form.Item label="文章作者" name="author_id">
              <Select 
                v-model:value="formData.author_id" 
                placeholder="请选择文章作者"
                :options="authorOptions"
                :loading="authorStore.loading"
                show-search
                :filter-option="(input: string, option: any) => 
                  option.label.toLowerCase().includes(input.toLowerCase())
                "
              />
            </Form.Item>
            
            <Form.Item label="文章标签" name="tag_ids">
              <Select 
                v-model:value="formData.tag_ids" 
                mode="multiple"
                placeholder="请选择文章标签"
                :options="tagOptions"
                :loading="tagStore.loading"
                show-search
                :filter-option="(input: string, option: any) => 
                  option.label.toLowerCase().includes(input.toLowerCase())
                "
              />
            </Form.Item>
            
            <Form.Item label="发布时间" name="publish_time">
              <DatePicker
                v-model:value="publishTimeValue"
                show-time
                format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择发布时间"
                class="w-full"
              />
            </Form.Item>
            
            <Form.Item label="文章状态" name="status">
              <Select 
                v-model:value="formData.status" 
                placeholder="请选择文章状态"
                :options="ARTICLE_STATUS_OPTIONS"
              />
            </Form.Item>
            
            <Form.Item label="是否可见" name="is_visible">
              <Switch 
                v-model:checked="formData.is_visible"
                checked-children="可见"
                un-checked-children="隐藏"
              />
            </Form.Item>
          </Card>
          
          <Card title="SEO设置" class="mb-6">
            <Form.Item label="SEO标题" name="seo_title">
              <Input 
                v-model:value="formData.seo_title" 
                placeholder="请输入SEO标题（最多100个字符）"
                :maxlength="100"
                show-count
              />
            </Form.Item>
            
            <Form.Item label="SEO关键字" name="seo_keywords">
              <Input 
                v-model:value="formData.seo_keywords" 
                placeholder="请输入SEO关键字，多个关键字用逗号分隔"
                :maxlength="100"
                show-count
              />
            </Form.Item>
            
            <Form.Item label="SEO描述" name="seo_description">
              <Input.TextArea 
                v-model:value="formData.seo_description" 
                placeholder="请输入SEO描述（最多300个字符）"
                :maxlength="300"
                :rows="3"
                show-count
              />
            </Form.Item>
          </Card>
        </Col>
      </Row>
      
      <!-- 操作按钮 -->
      <div class="mt-6 text-center">
        <Button 
          size="large" 
          class="mr-4"
          @click="handleCancel"
        >
          取消
        </Button>
        <Button
          type="primary"
          size="large"
          :loading="loading"
          @click="handleSubmit"
        >
          {{ isEdit ? '更新文章' : '发布文章' }}
        </Button>
      </div>
    </Form>

    <!-- 微信文章爬取弹窗 -->
    <WechatCrawlerModal
      v-model:visible="showWechatCrawler"
      @crawl-success="handleWechatCrawlSuccess"
    />
    
    <!-- 测试HTML内容弹窗 -->
    <Modal
      v-model:open="showTestHtmlModal"
      title="设置HTML测试内容"
      @ok="setTestHtmlContent"
      @cancel="showTestHtmlModal = false"
    >
      <div class="mb-4">
        <p class="text-sm text-gray-600 mb-2">
          输入HTML内容进行测试，可以粘贴包含样式和图片的HTML代码：
        </p>
        <Input.TextArea
          v-model:value="testHtmlContent"
          :rows="8"
          placeholder="请输入HTML内容..."
        />
      </div>
    </Modal>
    
    <!-- 显示当前HTML内容弹窗 -->
    <Modal
      v-model:open="showCurrentHtmlModal"
      title="当前HTML内容"
      width="800px"
      @ok="showCurrentHtmlModal = false"
      @cancel="showCurrentHtmlModal = false"
    >
      <div class="mb-4">
        <p class="text-sm text-gray-600 mb-2">
          以下是当前编辑器中的HTML内容：
        </p>
        <Input.TextArea
          :value="currentHtmlContent"
          :rows="10"
          readonly
        />
      </div>
    </Modal>
  </div>
</template>

<style scoped>
.article-form {
  max-width: 1200px;
  margin: 0 auto;
}

:deep(.ant-card-head-title) {
  font-weight: 600;
}

:deep(.ant-form-item-label > label) {
  font-weight: 500;
}
</style>
