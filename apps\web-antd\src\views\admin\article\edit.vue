<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Card, message } from 'ant-design-vue';
import { useRouter, useRoute } from 'vue-router';

// 导入组件
import ArticleForm from '#/components/article/article-form.vue';

// 导入store
import { useArticleStore } from '#/store/article/article';

// 导入类型
import type { ArticleFormData, Article } from '#/types';

defineOptions({
  name: 'ArticleEdit',
});

const router = useRouter();
const route = useRoute();
const articleStore = useArticleStore();

// 表单引用
const articleFormRef = ref();

// 文章ID
const articleId = ref<number>(Number(route.params.id));

// 调试信息
console.log('路由参数:', route.params);
console.log('文章ID:', articleId.value);

// 文章数据
const articleData = ref<Article | null>(null);

// 加载文章详情
async function loadArticleDetail() {
  try {
    if (!articleId.value) {
      message.error('文章ID无效');
      router.push('/article-manage/list');
      return;
    }

    const data = await articleStore.getArticleDetail(articleId.value);
    articleData.value = data;
    
    // 设置表单数据
    if (articleFormRef.value && data) {
      articleFormRef.value.setFormData({
        title: data.title,
        style: '富文本', // 强制设置为富文本格式
        content: data.content,
        channel: data.channel,
        cover_image: data.cover_image || '',
        summary: data.summary || '',
        share_image: data.share_image || '',
        publish_time: data.publish_time,
        is_visible: data.is_visible,
        seo_title: data.seo_title || '',
        seo_keywords: data.seo_keywords || '',
        seo_description: data.seo_description || '',
        status: data.status,
        author_id: data.author_id,
        tag_ids: data.tags?.map(tag => tag.id) || [],
      });
    }
  } catch (error) {
    message.error('加载文章详情失败');
    console.error('加载失败:', error);
    router.push('/article-manage/list');
  }
}

// 处理文章更新
async function handleSubmit(formData: ArticleFormData) {
  try {
    console.log('更新文章ID:', articleId.value);
    console.log('更新数据:', formData);

    if (!articleId.value) {
      message.error('文章ID无效，无法更新');
      return;
    }

    await articleStore.updateArticle(articleId.value, formData);
    message.success('文章更新成功！');

    // 跳转到文章列表页面
    router.push('/article-manage/list');
  } catch (error) {
    message.error('文章更新失败，请重试');
    console.error('更新失败:', error);
  }
}

// 处理取消
function handleCancel() {
  router.push('/article-manage/list');
}

// 组件挂载时加载数据
onMounted(() => {
  loadArticleDetail();
});
</script>

<template>
  <div class="p-6">
    <Card>
      <!-- 页面标题 -->
      <div class="mb-6">
        <h2 class="text-xl font-semibold text-gray-900">编辑文章</h2>
        <p class="mt-1 text-sm text-gray-500">
          编辑文章内容和设置
          <span v-if="articleData" class="ml-2 text-blue-600">
            {{ articleData.title }}
          </span>
        </p>
      </div>

      <!-- 文章编辑表单 -->
      <ArticleForm
        ref="articleFormRef"
        :loading="articleStore.loading"
        :is-edit="true"
        @submit="handleSubmit"
        @cancel="handleCancel"
      />
    </Card>
  </div>
</template>
