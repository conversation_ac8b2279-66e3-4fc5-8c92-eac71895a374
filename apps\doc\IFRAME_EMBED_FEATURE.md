# 富文本编辑器iframe嵌入功能

## 功能概述

为wangEditor富文本编辑器添加了iframe嵌入外部网页链接的功能，用户可以在文章中嵌入外部网页内容，如视频、地图、代码演示等。

## 功能特性

### 1. 工具栏按钮
- 在富文本编辑器工具栏右侧添加了"🌐 嵌入网页"按钮
- 点击按钮打开iframe插入弹窗

### 2. 弹窗界面
- **快速选择**：提供常用网站预设（Bilibili、YouTube、CodePen、GitHub、高德地图、百度地图）
- **网页链接**：输入要嵌入的网页URL
- **标题**：可选，为嵌入的网页添加自定义标题
- **尺寸设置**：可设置iframe的宽度和高度
- **使用提示**：提供详细的使用说明和注意事项

### 3. 安全特性
- URL格式验证，确保输入的是有效的HTTP/HTTPS链接
- HTML转义处理，防止XSS攻击
- iframe sandbox属性，限制嵌入内容的权限
- 支持lazy loading，提升页面加载性能

### 4. 用户体验
- 美观的iframe容器设计，包含标题栏和网址显示
- 渐变色标题栏，提升视觉效果
- 右上角iframe标识，便于识别嵌入内容
- 响应式设计，适配不同屏幕尺寸

## 使用方法

### 1. 基本使用
1. 在富文本编辑器中点击"🌐 嵌入网页"按钮
2. 在弹窗中输入要嵌入的网页链接
3. 可选择设置标题和尺寸
4. 点击确定插入iframe

### 2. 快速选择
1. 点击弹窗中的常用网站按钮
2. 系统自动填充对应的URL、标题和推荐高度
3. 可根据需要调整参数
4. 点击确定插入

### 3. 自定义设置
- **宽度**：支持百分比（如100%）或像素值（如800px）
- **高度**：建议使用像素值，如400px、500px等
- **标题**：显示在iframe容器顶部的标题栏中

## 技术实现

### 1. 组件结构
```
rich-editor.vue
├── 工具栏扩展（自定义iframe按钮）
├── iframe插入弹窗
├── 表单验证和处理
└── HTML生成和插入
```

### 2. 核心功能
- `showIframeModal()` - 显示插入弹窗
- `selectCommonSite()` - 选择常用网站
- `insertIframe()` - 插入iframe到编辑器
- URL验证和安全处理

### 3. 生成的HTML结构
```html
<div class="iframe-container">
  <div class="iframe-header">
    <!-- 标题和URL显示 -->
  </div>
  <div>
    <iframe src="..." sandbox="..." loading="lazy">
    </iframe>
  </div>
</div>
```

## 支持的网站类型

### ✅ 推荐嵌入
- **视频平台**：Bilibili、YouTube（部分视频）
- **代码演示**：CodePen、JSFiddle、CodeSandbox
- **地图服务**：高德地图、百度地图
- **文档分享**：GitHub、GitLab
- **在线工具**：各类在线计算器、转换工具

### ❌ 不支持嵌入
- **社交平台**：微信、微博、QQ空间
- **电商平台**：淘宝、京东、拼多多
- **金融网站**：银行、支付平台
- **设置了X-Frame-Options的网站**

## 注意事项

### 1. 安全考虑
- 只允许HTTP和HTTPS协议的链接
- 使用iframe sandbox属性限制权限
- 对用户输入进行HTML转义处理

### 2. 兼容性
- 某些网站可能禁止iframe嵌入
- 建议在发布前测试嵌入效果
- 移动端可能存在显示差异

### 3. 性能优化
- 使用lazy loading延迟加载
- 建议合理设置iframe尺寸
- 避免在一篇文章中嵌入过多iframe

## 后续优化建议

1. **预览功能**：在插入前提供iframe预览
2. **模板管理**：允许用户自定义常用网站模板
3. **响应式设置**：提供移动端适配选项
4. **批量管理**：支持批量编辑已插入的iframe
5. **统计分析**：记录iframe的使用情况和点击率

## 更新日志

### v1.0.0 (2025-01-04)
- ✅ 基础iframe嵌入功能
- ✅ 安全验证和HTML转义
- ✅ 常用网站快速选择
- ✅ 美观的容器设计
- ✅ 响应式布局支持
