<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <meta content="ie=edge" http-equiv="X-UA-Compatible" />
    <title>测试页面</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html {
            font-size: 16px;
            line-height: 1.6;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            color: #333;
            background-color: #fff;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            word-wrap: break-word;
        }

        .article-header {
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }

        .article-title {
            font-size: 28px;
            font-weight: bold;
            line-height: 1.3;
            color: #222;
            margin-bottom: 10px;
        }

        .article-content {
            font-size: 16px;
            line-height: 1.8;
        }

        .article-content p {
            margin: 15px 0;
        }

        .article-content img {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 20px auto;
            border-radius: 4px;
        }
    </style>
</head>

<body>
    <div class="article-header">
        <h1 class="article-title">测试页面 - 检查显示是否正常</h1>
    </div>
    
    <div class="article-content">
        <p>这是一个测试页面，用来检查HTML文件是否能正常显示。</p>
        
        <p>如果您能看到这段文字，说明HTML文件可以正常在浏览器中打开。</p>
        
        <h2>测试内容</h2>
        <ul>
            <li>文字显示测试</li>
            <li>样式应用测试</li>
            <li>布局测试</li>
        </ul>
        
        <p>这个页面使用了与原始test.html相同的基础样式，但内容更简单。</p>
        
        <div style="background-color: #f0f0f0; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3>提示</h3>
            <p>如果这个简化版本可以正常显示，但原始的test.html显示空白，可能的原因包括：</p>
            <ol>
                <li>原始文件中的某些CSS样式导致内容隐藏</li>
                <li>文件编码问题</li>
                <li>某些特殊的HTML标签或属性导致渲染问题</li>
                <li>文件过大导致浏览器加载缓慢</li>
            </ol>
        </div>
        
        <p><strong>当前时间：</strong><span id="current-time"></span></p>
    </div>

    <script>
        // 显示当前时间，验证JavaScript是否正常工作
        document.getElementById('current-time').textContent = new Date().toLocaleString('zh-CN');
        
        // 在控制台输出信息
        console.log('测试页面加载完成');
        console.log('页面标题:', document.title);
        console.log('页面内容长度:', document.body.innerHTML.length);
    </script>
</body>
</html>
