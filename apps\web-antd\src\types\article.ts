// 文章管理相关类型定义

// 通用响应格式
export interface StanderResult<T = any> {
  success: boolean;
  data: T;
  message: string;
  status_code?: number;
  total_records?: number;
}

// 通用分页参数
export interface PaginationParams {
  page?: number;
  page_size?: number;
}

// 标签分类相关类型
export interface ArticleCategory {
  id?: number;
  name: string;
  weight: string;
  seo_title?: string;
  seo_keywords?: string;
  seo_description?: string;
  create_time?: string;
  update_time?: string;
  creator_id?: number;
  updater_id?: number;
}

export interface ArticleCategoryFormData {
  name: string;
  weight: string;
  seo_title?: string;
  seo_keywords?: string;
  seo_description?: string;
}

export interface ArticleCategoryApiData {
  name: string;
  weight: string;
  seo_title?: string;
  seo_keywords?: string;
  seo_description?: string;
}

export interface ArticleCategoryListParams extends PaginationParams {
  name?: string;
  weight?: string;
}

// 文章作者相关类型
export interface ArticleAuthor {
  id?: number;
  wechat_nickname: string;
  author_name: string;
  create_time?: string;
  update_time?: string;
  creator_id?: number;
  updater_id?: number;
}

export interface ArticleAuthorFormData {
  wechat_nickname: string;
  author_name: string;
}

export interface ArticleAuthorListParams extends PaginationParams {
  wechat_nickname?: string;
  author_name?: string;
}

// 文章标签相关类型
export interface ArticleTag {
  id?: number;
  name: string;
  first_letter?: string;
  category_id: number;
  article_count?: number;
  article_read_count?: number;
  create_time?: string;
  update_time?: string;
  creator_id?: number;
  updater_id?: number;
  category?: {
    id: number;
    name: string;
  };
}

export interface ArticleTagFormData {
  name: string;
  category_id: number;
}

export interface ArticleTagApiData {
  name: string;
  category_id: number;
}

export interface ArticleTagListParams extends PaginationParams {
  name?: string;
  first_letter?: string;
  category_id?: number;
  category_weight?: string;
}

// 文章相关类型
export interface Article {
  id?: number;
  title: string;
  style: string;
  content: string;
  channel: string;
  cover_image?: string;
  summary?: string;
  share_image?: string;
  publish_time: string;
  is_visible?: boolean;
  seo_title?: string;
  seo_keywords?: string;
  seo_description?: string;
  view_count?: number;
  like_count?: number;
  favorite_count?: number;
  comment_count?: number;
  status: string;
  author_id: number;
  create_time?: string;
  update_time?: string;
  creator_id?: number;
  updater_id?: number;
  author?: {
    author_name: string;
    id: number;
    wechat_nickname: string;
  };
  tags?: Array<{
    first_letter: string;
    id: number;
    name: string;
  }>;
}

export interface ArticleFormData {
  title: string;
  style: string;
  content: string;
  channel: string;
  cover_image?: string;
  summary?: string;
  share_image?: string;
  publish_time: string;
  is_visible?: boolean;
  seo_title?: string;
  seo_keywords?: string;
  seo_description?: string;
  status: string;
  author_id: number;
  tag_ids: number[];
}

export interface ArticleListParams extends PaginationParams {
  title?: string;
  author_name?: string;
  tag_id?: number;
  channel?: string;
  status?: string;
  publish_start?: string;
  publish_end?: string;
}

// 分类权重选项
export const CATEGORY_WEIGHT_OPTIONS = [
  { label: '1.合作伙伴（服务商及工厂）', value: '1' },
  {
    label: '2.文章性质（实操干货、政策解读、突发新闻、跨境活动等）',
    value: '2',
  },
  { label: '3.服务品类（物流、软件、金融等等）、产业带', value: '3' },
  { label: '4.国家/地区', value: '4' },
  { label: '5.平台/独立站', value: '5' },
];

// 文章风格选项 (已弃用 - 现在所有文章都默认使用富文本格式)
export const ARTICLE_STYLE_OPTIONS = [
  { label: '富文本', value: '富文本' },
  { label: 'markdown', value: 'markdown' },
];

// 发布频道选项
export const ARTICLE_CHANNEL_OPTIONS = [
  { label: '头条', value: '头条' },
  { label: '百科', value: '百科' },
  { label: '快讯', value: '快讯' },
];

// 文章状态选项
export const ARTICLE_STATUS_OPTIONS = [
  { label: '草稿', value: '0' },
  { label: '待审核', value: '1' },
  { label: '审核中', value: '2' },
  { label: '已发布', value: '3' },
];
