# 微信公众号HTML内容正确渲染指南

## 概述

本文档详细说明如何正确处理和渲染微信公众号的HTML格式内容，确保富文本编辑器能够正确显示和编辑这些内容。

## 问题背景

微信公众号的HTML内容通常包含：
- 复杂的内联样式
- 微信特有的属性（如 `data-src`）
- 不规范的HTML结构
- 大量的无用标签和属性

这些特点导致直接将HTML内容设置到富文本编辑器时可能出现：
- 样式丢失或错乱
- 图片无法显示
- 编辑器无法正确解析内容
- 格式不兼容

## 解决方案

### 1. HTML内容清理

我们实现了专门的HTML清理函数 `cleanHtmlContent`，它能够：

#### 移除危险和无用内容
- 移除 `<script>` 标签
- 移除 `<style>` 标签
- 移除HTML注释
- 移除微信特有的 `data-*` 属性

#### 处理图片
- 将 `data-src` 转换为 `src`
- 添加响应式样式 `max-width: 100%; height: auto;`
- 保留 `alt` 属性

#### 标签白名单过滤
只保留富文本编辑器支持的标签：
- 文本格式：`p`, `br`, `strong`, `b`, `em`, `i`, `u`
- 标题：`h1`, `h2`, `h3`, `h4`, `h5`, `h6`
- 列表：`ul`, `ol`, `li`
- 其他：`blockquote`, `a`, `img`, `span`, `div`

#### 样式属性过滤
只保留安全的样式属性：
- `color` - 文字颜色
- `font-size` - 字体大小
- `font-weight` - 字体粗细
- `text-align` - 文本对齐
- `text-decoration` - 文本装饰
- `background-color` - 背景颜色

### 2. 富文本编辑器集成

#### 新增方法
在 `RichEditor` 组件中新增了 `setHtmlContent` 方法：

```typescript
const setHtmlContent = (html: string) => {
  if (editorRef.value) {
    try {
      // 清空编辑器内容
      editorRef.value.clear();
      // 使用 dangerouslyInsertHtml 插入外部HTML
      editorRef.value.dangerouslyInsertHtml(html);
    } catch (error) {
      // 降级处理方案
      editorRef.value.setHtml(html);
    }
  }
};
```

#### 使用方式
```typescript
// 在文章表单中使用
if (richEditorRef.value && richEditorRef.value.setHtmlContent) {
  richEditorRef.value.setHtmlContent(cleanedHtml);
}
```

### 3. 完整处理流程

```mermaid
graph TD
    A[微信公众号HTML] --> B[cleanHtmlContent清理]
    B --> C[移除script/style标签]
    C --> D[处理图片属性]
    D --> E[标签白名单过滤]
    E --> F[样式属性过滤]
    F --> G[清理空标签和多余空白]
    G --> H[setHtmlContent设置到编辑器]
    H --> I[dangerouslyInsertHtml插入]
    I --> J[富文本编辑器正确显示]
```

## 使用示例

### 1. 基本使用

```typescript
import { useWechatCrawlerStore } from '#/store/article/wechat-crawler';

const crawlerStore = useWechatCrawlerStore();

// 清理HTML内容
const cleanedHtml = crawlerStore.cleanHtmlContent(rawHtml);

// 设置到富文本编辑器
if (richEditorRef.value) {
  richEditorRef.value.setHtmlContent(cleanedHtml);
}
```

### 2. 在微信文章导入中使用

```typescript
function handleWechatCrawlSuccess(data: WechatArticleInfo) {
  if (data.content) {
    // 使用专用方法设置HTML内容
    if (richEditorRef.value && richEditorRef.value.setHtmlContent) {
      richEditorRef.value.setHtmlContent(data.content);
    }
  }
}
```

## 测试页面

我们提供了专门的测试页面 `/article-manage/wechat-html-test`，可以：

1. **输入测试HTML**：粘贴微信公众号的HTML内容
2. **处理和清理**：使用清理函数处理HTML
3. **富文本显示**：在富文本编辑器中显示处理结果
4. **多视图对比**：
   - 清理后的HTML代码
   - 预览效果
   - 编辑器输出内容

## 最佳实践

### 1. 内容获取
- 推荐使用"手动粘贴"方式获取微信文章内容
- 确保粘贴的是完整的HTML格式内容

### 2. 内容处理
- 始终使用 `cleanHtmlContent` 函数清理HTML
- 使用 `setHtmlContent` 方法设置到编辑器

### 3. 错误处理
- 实现降级处理方案
- 提供用户友好的错误提示

### 4. 性能优化
- 对大量HTML内容进行分批处理
- 使用防抖处理用户输入

## 常见问题

### Q: 图片无法显示？
A: 检查图片链接是否有效，确保 `data-src` 已转换为 `src`

### Q: 样式丢失？
A: 检查样式是否在白名单中，复杂样式可能被过滤

### Q: 编辑器无法解析内容？
A: 使用 `dangerouslyInsertHtml` 方法，或检查HTML结构是否正确

### Q: 内容格式错乱？
A: 确保HTML标签配对正确，移除不支持的标签

## 技术细节

### wangEditor API
- `editor.clear()` - 清空编辑器内容
- `editor.dangerouslyInsertHtml(html)` - 插入外部HTML
- `editor.setHtml(html)` - 设置编辑器HTML（仅用于编辑器输出的HTML）
- `editor.getHtml()` - 获取编辑器HTML内容

### 支持的HTML标签
```javascript
const allowedTags = [
  'p', 'br', 'strong', 'b', 'em', 'i', 'u',
  'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
  'ul', 'ol', 'li', 'blockquote', 'a', 'img',
  'span', 'div'
];
```

### 安全的样式属性
```javascript
const safeStyles = [
  'color', 'font-size', 'font-weight',
  'text-align', 'text-decoration', 'background-color'
];
```

## 更新日志

- **v1.0.0** - 初始实现HTML清理和富文本编辑器集成
- **v1.1.0** - 新增测试页面和完善错误处理
- **v1.2.0** - 优化图片处理和样式过滤逻辑
