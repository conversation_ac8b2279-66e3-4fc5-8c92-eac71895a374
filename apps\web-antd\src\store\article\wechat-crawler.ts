// 微信公众号文章爬取工具类
import { defineStore } from 'pinia';
import { ref } from 'vue';
import { requestClient } from '#/api/request';

// 微信文章信息接口
export interface WechatArticleInfo {
  title: string;
  content: string;
  author: string;
  publish_time: string;
  cover_image?: string;
  summary?: string;
  original_url?: string;
  crawl_time?: string;
}

// URL验证结果接口
export interface UrlValidationResult {
  url: string;
  is_valid: boolean;
  message: string;
}

// API响应接口
export interface ApiResponse<T = any> {
  success: boolean;
  data: T | null;
  message: string;
}



// API函数 - 验证微信文章URL
function _validateWechatUrl(url: string) {
  console.log('发送URL验证请求:', url);
  return requestClient.get<ApiResponse<UrlValidationResult>>(
    'article/validate-wechat-url',
    {
      params: { url },
      // 添加请求头确保正确路由
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
}

// API函数 - 爬取微信文章
function _crawlWechatArticle(url: string) {
  console.log('发送文章爬取请求:', url);
  return requestClient.post<ApiResponse<WechatArticleInfo>>(
    'article/crawl-wechat',
    { url }
  );
}

export const useWechatCrawlerStore = defineStore('wechatCrawler', () => {
  // 状态
  const loading = ref(false);
  const validating = ref(false);
  const lastCrawledData = ref<WechatArticleInfo | null>(null);

  /**
   * 验证微信文章URL
   * @param url 微信文章链接
   * @returns 验证结果
   */
  async function validateWechatUrl(url: string): Promise<boolean> {
    try {
      validating.value = true;

      // 先进行前端基础格式验证
      if (!isValidWechatUrl(url)) {
        console.log('前端格式验证失败:', url);
        return false;
      }

      console.log('开始后端URL验证:', url);

      // 调用后端接口进行深度验证
      const response = await _validateWechatUrl(url);

      console.log('后端验证响应:', response);

      if (response.success && response.data) {
        return response.data.is_valid;
      }

      return false;
    } catch (error: any) {
      console.error('URL验证失败:', error);
      console.error('错误详情:', error.response?.data);
      return false;
    } finally {
      validating.value = false;
    }
  }

  /**
   * 爬取微信公众号文章
   * @param url 微信文章链接
   * @returns 爬取结果
   */
  async function crawlArticle(url: string): Promise<WechatArticleInfo> {
    try {
      loading.value = true;

      // 1. 前端基础验证
      if (!isValidWechatUrl(url)) {
        throw new Error('无效的微信公众号文章链接格式');
      }

      // 2. 尝试后端URL验证（如果失败则跳过）
      try {
        const isValid = await validateWechatUrl(url);
        if (!isValid) {
          throw new Error('后端验证失败：无效的微信公众号文章链接');
        }
      } catch (validationError: any) {
        console.warn('URL验证接口调用失败，跳过验证直接爬取:', validationError);
        // 如果验证接口有问题，我们继续进行爬取
      }

      // 3. 调用后端爬取接口
      console.log('开始爬取文章:', url);
      const response = await _crawlWechatArticle(url);

      console.log('爬取响应:', response);

      if (response.success && response.data) {
        lastCrawledData.value = response.data;
        return response.data;
      } else {
        throw new Error(response.message || '文章爬取失败');
      }
    } catch (error: any) {
      console.error('爬取过程出错:', error);

      // 如果是网络错误或后端不可用，提供友好提示
      if (error.response?.status === 404) {
        throw new Error('爬取服务暂时不可用，请稍后重试');
      } else if (error.response?.status === 400) {
        throw new Error('请求参数错误，请检查文章链接格式');
      } else if (error.response?.status === 500) {
        throw new Error('服务器内部错误，请联系管理员');
      }

      throw error;
    } finally {
      loading.value = false;
    }
  }

  /**
   * 验证微信文章URL格式（前端基础验证）
   * @param url 待验证的URL
   * @returns 是否为有效的微信文章链接格式
   */
  function isValidWechatUrl(url: string): boolean {
    if (!url || typeof url !== 'string') {
      return false;
    }

    const wechatUrlPatterns = [
      /^https?:\/\/mp\.weixin\.qq\.com\/s\/.+/,
      /^https?:\/\/mp\.weixin\.qq\.com\/s\?.+/,
    ];

    return wechatUrlPatterns.some(pattern => pattern.test(url.trim()));
  }

  /**
   * 清理HTML内容，移除不必要的标签和样式，保留富文本编辑器支持的格式
   * @param html 原始HTML内容
   * @returns 清理后的HTML内容
   */
  function cleanHtmlContent(html: string): string {
    if (!html || html.trim() === '') {
      return '';
    }
    
    // 移除script标签
    html = html.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '');

    // 移除style标签
    html = html.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '');

    // 移除注释
    html = html.replace(/<!--[\s\S]*?-->/gi, '');

    // 处理图片：将data-src转换为src，移除多余属性
    html = html.replace(/<img[^>]*>/gi, (match) => {
      // 提取src或data-src
      const srcMatch = match.match(/(?:src|data-src)\s*=\s*["']([^"']*?)["']/i);
      const altMatch = match.match(/alt\s*=\s*["']([^"']*?)["']/i);

      if (srcMatch && srcMatch[1]) {
        const src = srcMatch[1];
        const alt = altMatch ? altMatch[1] : '';
        // 保留更多样式属性，确保图片正确显示
        return `<img src="${src}" alt="${alt}" style="max-width: 100%; height: auto; display: block; margin: 10px 0;" />`;
      }
      return match;
    });

    // 保留基本的富文本格式标签，移除其他复杂属性
    const allowedTags = ['p', 'br', 'strong', 'b', 'em', 'i', 'u', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
                        'ul', 'ol', 'li', 'blockquote', 'a', 'img', 'span', 'div'];

    // 清理标签属性，只保留必要的
    html = html.replace(/<(\w+)([^>]*)>/gi, (match, tagName, attributes) => {
      if (!allowedTags.includes(tagName.toLowerCase())) {
        // 不支持的标签转换为div或span
        if (['section', 'article', 'header', 'footer', 'main'].includes(tagName.toLowerCase())) {
          return '<div>';
        }
        return '';
      }

      let cleanAttributes = '';

      // 对于特定标签保留特定属性
      if (tagName.toLowerCase() === 'a') {
        const hrefMatch = attributes.match(/href\s*=\s*["']([^"']*?)["']/i);
        if (hrefMatch) {
          cleanAttributes = ` href="${hrefMatch[1]}" target="_blank"`;
        }
      } else if (tagName.toLowerCase() === 'img') {
        // img标签的属性已经在上面处理过了
        return match;
      } else if (['span', 'div', 'p'].includes(tagName.toLowerCase())) {
        // 保留基本样式
        const styleMatch = attributes.match(/style\s*=\s*["']([^"']*?)["']/i);
        if (styleMatch) {
          const style = styleMatch[1];
          // 保留更多安全的样式属性，确保格式正确显示
          const safeStyles = style.match(/(color|font-size|font-weight|text-align|text-decoration|background-color|line-height|margin|padding|display):\s*[^;]+/gi);
          if (safeStyles && safeStyles.length > 0) {
            cleanAttributes = ` style="${safeStyles.join('; ')}"`;
          }
        }
      }

      return `<${tagName}${cleanAttributes}>`;
    });

    // 移除不支持的结束标签
    html = html.replace(/<\/(\w+)>/gi, (match, tagName) => {
      if (!allowedTags.includes(tagName.toLowerCase())) {
        if (['section', 'article', 'header', 'footer', 'main'].includes(tagName.toLowerCase())) {
          return '</div>';
        }
        return '';
      }
      return match;
    });

    // 清理多余的空白和空标签
    html = html.replace(/\s+/g, ' '); // 多个空格合并为一个
    html = html.replace(/<(\w+)[^>]*>\s*<\/\1>/gi, ''); // 移除空标签
    html = html.replace(/(<br\s*\/?>)\s*(<br\s*\/?>)/gi, '$1'); // 合并连续的br标签

    // 确保段落结构正确
    html = html.replace(/(<\/p>)\s*([^<])/gi, '$1<p>$2'); // 段落外的文本包装到p标签中

    return html.trim();
  }

  /**
   * 提取文章摘要
   * @param content HTML内容
   * @param maxLength 最大长度
   * @returns 文章摘要
   */
  function extractSummary(content: string, maxLength: number = 200): string {
    // 移除HTML标签
    const textContent = content.replace(/<[^>]*>/g, '');
    
    // 移除多余的空白字符
    const cleanText = textContent.replace(/\s+/g, ' ').trim();
    
    // 截取指定长度
    if (cleanText.length <= maxLength) {
      return cleanText;
    }
    
    return cleanText.substring(0, maxLength) + '...';
  }

  /**
   * 获取最后爬取的数据
   */
  function getLastCrawledData(): WechatArticleInfo | null {
    return lastCrawledData.value;
  }

  /**
   * 清除缓存数据
   */
  function clearCache(): void {
    lastCrawledData.value = null;
  }

  /**
   * 解析用户粘贴的内容
   * @param pastedContent 用户粘贴的HTML或文本内容
   * @param title 文章标题（可选）
   * @returns 解析后的文章信息
   */
  function parseUserPastedContent(pastedContent: string, title?: string): WechatArticleInfo {
    try {
      let content = pastedContent;
      let extractedTitle = title || '';

      // 如果内容包含HTML标签，说明是富文本内容
      if (/<[^>]+>/.test(content)) {
        // 尝试从HTML中提取标题
        if (!extractedTitle) {
          const titleMatch = content.match(/<h[1-6][^>]*>(.*?)<\/h[1-6]>/i);
          if (titleMatch && titleMatch[1]) {
            extractedTitle = titleMatch[1].replace(/<[^>]+>/g, '').trim();
          }
        }

        // 清理HTML内容
        content = cleanHtmlContent(content);
      } else {
        // 纯文本内容，转换为HTML格式
        content = content
          .split('\n')
          .filter(line => line.trim())
          .map(line => `<p>${line.trim()}</p>`)
          .join('\n');
      }

      // 生成摘要
      const summary = extractSummary(content, 200);

      return {
        title: extractedTitle || '请输入文章标题',
        content,
        author: '微信公众号',
        publish_time: new Date().toISOString(),
        summary,
      };
    } catch (error) {
      console.error('解析粘贴内容失败:', error);
      throw new Error('内容解析失败');
    }
  }

  /**
   * 从剪贴板获取内容
   * @returns 剪贴板内容
   */
  async function getClipboardContent(): Promise<string> {
    try {
      if (navigator.clipboard && navigator.clipboard.readText) {
        return await navigator.clipboard.readText();
      } else {
        throw new Error('浏览器不支持剪贴板API');
      }
    } catch (error) {
      console.warn('获取剪贴板内容失败:', error);
      throw new Error('无法获取剪贴板内容，请手动粘贴');
    }
  }

  return {
    // 状态
    loading,
    validating,
    lastCrawledData,

    // 方法
    validateWechatUrl,
    crawlArticle,
    isValidWechatUrl,
    parseUserPastedContent,
    getClipboardContent,
    cleanHtmlContent,
    extractSummary,
    getLastCrawledData,
    clearCache,
  };
});
