<script setup lang="ts">
import '@wangeditor/editor/dist/css/style.css'; // 引入css
import { onBeforeUnmount, ref, shallowRef, watch, computed } from 'vue';
// @ts-ignore
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import type { IDomEditor, IEditorConfig, IToolbarConfig } from '@wangeditor/editor';

// 导入图片上传store
import { useImageStore } from '#/store/image/image';

defineOptions({
  name: 'RichEditor',
});

const props = defineProps<{
  modelValue: string;
  placeholder?: string;
  disabled?: boolean;
  height?: string | number;
}>();

const emit = defineEmits<{
  'update:modelValue': [value: string];
  change: [value: string];
}>();

// 使用图片store
const imageStore = useImageStore();

// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef<IDomEditor>();

// 内容 HTML
const valueHtml = ref('');

// 工具栏配置
const toolbarConfig: Partial<IToolbarConfig> = {
  excludeKeys: [
    'group-video', // 排除视频
    'fullScreen', // 排除全屏
  ],
};

// 编辑器配置
const editorConfig: Partial<IEditorConfig> = {
  placeholder: props.placeholder || '请输入内容...',
  // 配置HTML内容处理相关选项
  hoverbarKeys: {
    // 自定义悬浮菜单
  },
  MENU_CONF: {
    // 配置上传图片
    uploadImage: {
      // 自定义上传
      async customUpload(file: File, insertFn: Function) {
        try {
          // 使用图片管理接口上传
          const imageInfo = await imageStore.uploadImage({
            file,
            original_name: file.name,
            category: 'article',
            is_public: true,
          });

          // 插入图片到编辑器
          insertFn(imageInfo.access_url, imageInfo.original_name, imageInfo.access_url);
        } catch (error) {
          console.error('图片上传失败:', error);
          // 上传失败时使用本地预览
          const url = URL.createObjectURL(file);
          insertFn(url, file.name, url);
        }
      },
    },
    // 配置上传视频
    uploadVideo: {
      // 自定义上传
      async customUpload(file: File, insertFn: Function) {
        // 这里可以实现视频上传逻辑
        // 暂时使用本地预览
        const url = URL.createObjectURL(file);
        insertFn(url);
      },
    },
  },
  // 重要：忽略XSS过滤，保留更多HTML标签和属性
  xssIgnore: true // 更标准地保留HTML内容
};

// 监听props变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue !== valueHtml.value) {
      valueHtml.value = newValue;
    }
  },
  { immediate: true }
);

// 监听disabled变化
watch(
  () => props.disabled,
  (disabled) => {
    if (editorRef.value) {
      if (disabled) {
        editorRef.value.disable();
      } else {
        editorRef.value.enable();
      }
    }
  }
);

// 处理创建编辑器
const handleCreated = (editor: IDomEditor) => {
  editorRef.value = editor;

  // 设置初始状态
  if (props.disabled) {
    editor.disable();
  }
};

// 暴露方法给父组件使用
const setHtmlContent = (html: string) => {
  console.log('准备设置HTML内容，原始长度:', html.length);
  console.log('原始HTML内容预览:', html.substring(0, 200) + '...');
  
  // 方法1：直接通过v-model设置（最简单可靠的方式）
  valueHtml.value = html;
  emit('update:modelValue', html);

  // 如果编辑器已经存在，尝试强制刷新
  if (editorRef.value && !editorRef.value.isDestroyed) {
    try {
      // 先获取当前内容
      const currentHtml = editorRef.value.getHtml();
      console.log('编辑器当前内容长度:', currentHtml.length);

      // 如果内容不同，尝试设置
      if (currentHtml !== html) {
        // 方法2：使用dangerouslyInsertHtml（适用于外部HTML，优先使用）
        try {
          console.log('使用dangerouslyInsertHtml方法设置HTML内容');
          // 恢复选区
          editorRef.value.restoreSelection();
          editorRef.value.clear();
          // 使用setTimeout确保在下一个事件循环中执行，提高成功率
          setTimeout(() => {
            editorRef.value.dangerouslyInsertHtml(html);
            // 验证内容是否设置成功
            const newHtml = editorRef.value.getHtml();
            console.log('设置后的内容长度:', newHtml.length);
            console.log('设置后的内容预览:', newHtml.substring(0, 200) + '...');
          }, 0);
        } catch (insertError) {
          console.error('dangerouslyInsertHtml方法执行失败:', insertError);
          // 方法3：使用setHtml（适用于wangEditor生成的HTML）
          try {
            console.log('使用setHtml方法设置HTML内容');
            editorRef.value.setHtml(html);
            // 验证内容是否设置成功
            const newHtml = editorRef.value.getHtml();
            console.log('setHtml后的内容长度:', newHtml.length);
            console.log('setHtml后的内容预览:', newHtml.substring(0, 200) + '...');
          } catch (setHtmlError) {
            console.warn('富文本编辑器内容设置失败，依赖v-model更新:', setHtmlError);
          }
        }
      } else {
        console.log('内容相同，无需更新');
      }
    } catch (error) {
      console.error('富文本编辑器操作失败:', error);
    }
  } else {
    console.log('编辑器未初始化或已销毁');
  }
};

// 暴露方法
defineExpose({
  setHtmlContent,
  getEditor: () => editorRef.value,
});

// 处理内容变化
const handleChange = (editor: IDomEditor) => {
  const html = editor.getHtml();
  valueHtml.value = html;
  emit('update:modelValue', html);
  emit('change', html);
};

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor == null) return;
  editor.destroy();
});

// 计算编辑器高度
const editorHeight = computed(() => {
  if (typeof props.height === 'number') {
    return `${props.height}px`;
  }
  return props.height || '400px';
});
</script>

<template>
  <div class="rich-editor">
    <Toolbar
      :editor="editorRef"
      :defaultConfig="toolbarConfig"
      :mode="'default'"
      class="border-b border-gray-200"
    />
    <Editor
      v-model="valueHtml"
      :defaultConfig="editorConfig"
      :mode="'default'"
      :style="{ height: editorHeight }"
      class="border border-gray-200 border-t-0"
      @onCreated="handleCreated"
      @onChange="handleChange"
    />
  </div>
</template>

<style scoped>
.rich-editor {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

.rich-editor:focus-within {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
</style>

<style>
/* 编辑器样式 */
.w-e-text-placeholder {
  color: #bfbfbf !important;
}

.w-e-text-container {
  background-color: #fff !important;
}

.w-e-text-container [data-slate-editor] {
  padding: 15px !important;
  line-height: 1.6 !important;
}

.w-e-toolbar {
  background-color: #fafafa !important;
  border-bottom: 1px solid #d9d9d9 !important;
}

.w-e-toolbar .w-e-bar-item button {
  color: #595959 !important;
}

.w-e-toolbar .w-e-bar-item button:hover {
  color: #1890ff !important;
  background-color: #e6f7ff !important;
}

.w-e-toolbar .w-e-bar-item.w-e-bar-item-active button {
  color: #1890ff !important;
  background-color: #e6f7ff !important;
}
</style>
