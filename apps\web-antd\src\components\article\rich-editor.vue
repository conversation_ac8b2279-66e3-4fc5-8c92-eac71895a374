<script setup lang="ts">
import '@wangeditor/editor/dist/css/style.css'; // 引入css
import { onBeforeUnmount, ref, shallowRef, watch, computed } from 'vue';
// @ts-ignore
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import type { IDomEditor, IEditorConfig, IToolbarConfig } from '@wangeditor/editor';
import { Modal, Input, Button, Form, message } from 'ant-design-vue';

// 导入图片上传store
import { useImageStore } from '#/store/image/image';

defineOptions({
  name: 'RichEditor',
});

const props = defineProps<{
  modelValue: string;
  placeholder?: string;
  disabled?: boolean;
  height?: string | number;
}>();

const emit = defineEmits<{
  'update:modelValue': [value: string];
  change: [value: string];
}>();

// 使用图片store
const imageStore = useImageStore();

// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef<IDomEditor>();

// 内容 HTML
const valueHtml = ref('');

// iframe弹窗相关状态
const iframeModalVisible = ref(false);
const iframeForm = ref({
  url: '',
  width: '100%',
  height: '400px',
  title: ''
});

// 常用网站预设
const commonSites = [
  { name: 'Bilibili视频', url: 'https://www.bilibili.com', height: '500px' },
  { name: 'YouTube视频', url: 'https://www.youtube.com', height: '500px' },
  { name: 'CodePen代码演示', url: 'https://codepen.io', height: '400px' },
  { name: 'GitHub仓库', url: 'https://github.com', height: '600px' },
  { name: '高德地图', url: 'https://www.amap.com', height: '400px' },
  { name: '百度地图', url: 'https://map.baidu.com', height: '400px' }
];

// 工具栏配置
const toolbarConfig: Partial<IToolbarConfig> = {
  excludeKeys: [
    'group-video', // 排除视频
    'fullScreen', // 排除全屏
  ]
};

// 编辑器配置
const editorConfig: Partial<IEditorConfig> = {
  placeholder: props.placeholder || '请输入内容...',
  // 配置HTML内容处理相关选项
  hoverbarKeys: {
    // 自定义悬浮菜单
  },
  MENU_CONF: {
    // 配置上传图片
    uploadImage: {
      // 自定义上传
      async customUpload(file: File, insertFn: Function) {
        try {
          // 使用图片管理接口上传
          const imageInfo = await imageStore.uploadImage({
            file,
            original_name: file.name,
            category: 'article',
            is_public: true,
          });

          // 插入图片到编辑器
          insertFn(imageInfo.access_url, imageInfo.original_name, imageInfo.access_url);
        } catch (error) {
          console.error('图片上传失败:', error);
          // 上传失败时使用本地预览
          const url = URL.createObjectURL(file);
          insertFn(url, file.name, url);
        }
      },
    },
    // 配置上传视频
    uploadVideo: {
      // 自定义上传
      async customUpload(file: File, insertFn: Function) {
        // 这里可以实现视频上传逻辑
        // 暂时使用本地预览
        const url = URL.createObjectURL(file);
        insertFn(url);
      },
    },
  },
  // 重要：忽略XSS过滤，保留更多HTML标签和属性
  // xssIgnore: true // 更标准地保留HTML内容
};

// 监听props变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue !== valueHtml.value) {
      valueHtml.value = newValue;
    }
  },
  { immediate: true }
);

// 监听disabled变化
watch(
  () => props.disabled,
  (disabled) => {
    if (editorRef.value) {
      if (disabled) {
        editorRef.value.disable();
      } else {
        editorRef.value.enable();
      }
    }
  }
);

// 显示iframe插入弹窗
const showIframeModal = () => {
  iframeModalVisible.value = true;
  // 重置表单
  iframeForm.value = {
    url: '',
    width: '100%',
    height: '400px',
    title: ''
  };
};

// 选择常用网站
const selectCommonSite = (site: typeof commonSites[0]) => {
  iframeForm.value.url = site.url;
  iframeForm.value.height = site.height;
  iframeForm.value.title = site.name;
};

// 插入iframe到编辑器
const insertIframe = () => {
  if (!iframeForm.value.url) {
    message.error('请输入网页链接');
    return;
  }

  if (!editorRef.value) {
    message.error('编辑器未初始化');
    return;
  }

  // 验证URL格式
  try {
    const url = new URL(iframeForm.value.url);
    // 确保是http或https协议
    if (!['http:', 'https:'].includes(url.protocol)) {
      message.error('请输入有效的HTTP或HTTPS链接');
      return;
    }
  } catch {
    message.error('请输入有效的网页链接');
    return;
  }

  // 清理和验证尺寸值
  const width = iframeForm.value.width || '100%';
  const height = iframeForm.value.height || '400px';

  // 构建iframe HTML - 使用更安全的方式
  const safeUrl = iframeForm.value.url.replace(/"/g, '&quot;');
  const safeTitle = (iframeForm.value.title || '嵌入网页').replace(/"/g, '&quot;');

  const iframeHtml = `
    <div class="iframe-container" style="margin: 15px 0; border: 1px solid #e1e5e9; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
      <div class="iframe-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px 16px; font-size: 14px; font-weight: 500; display: flex; align-items: center; gap: 8px;">
        <span style="font-size: 16px;">🌐</span>
        <div style="flex: 1; min-width: 0;">
          <div style="font-weight: 600; margin-bottom: 2px;">${safeTitle}</div>
          <div style="font-size: 12px; opacity: 0.9; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${safeUrl}</div>
        </div>
      </div>
      <div style="position: relative; background: #f8f9fa;">
        <iframe
          src="${safeUrl}"
          width="100%"
          height="${height}"
          frameborder="0"
          allowfullscreen
          sandbox="allow-scripts allow-same-origin allow-popups allow-forms"
          loading="lazy"
          style="display: block; border: none;"
          title="${safeTitle}"
        ></iframe>
        <div style="position: absolute; top: 8px; right: 8px; background: rgba(0,0,0,0.7); color: white; padding: 4px 8px; border-radius: 4px; font-size: 11px; pointer-events: none;">
          iframe
        </div>
      </div>
    </div>
    <p><br></p>
  `;

  // 插入到编辑器
  editorRef.value.dangerouslyInsertHtml(iframeHtml);

  // 关闭弹窗
  iframeModalVisible.value = false;
  message.success('网页嵌入成功！您可以继续编辑其他内容。');
};

// 注册自定义iframe菜单 - 简化版本，直接在工具栏配置中处理
const registerIframeMenu = () => {
  // 这个函数现在主要用于未来扩展
  console.log('iframe菜单已准备就绪');
};

// 处理创建编辑器
const handleCreated = (editor: IDomEditor) => {
  editorRef.value = editor;

  // 注册自定义iframe菜单
  registerIframeMenu();

  // 设置初始状态
  if (props.disabled) {
    editor.disable();
  }
};

// 暴露方法给父组件使用
const setHtmlContent = (html: string) => {
  console.log('准备设置HTML内容，原始长度:', html.length);
  console.log('原始HTML内容预览:', html.substring(0, 200) + '...');
  
  // 方法1：直接通过v-model设置（最简单可靠的方式）
  valueHtml.value = html;
  emit('update:modelValue', html);

  // 如果编辑器已经存在，尝试强制刷新
  if (editorRef.value && !editorRef.value.isDestroyed) {
    try {
      // 先获取当前内容
      const currentHtml = editorRef.value.getHtml();
      console.log('编辑器当前内容长度:', currentHtml.length);

      // 如果内容不同，尝试设置
      if (currentHtml !== html) {
        // 方法2：使用dangerouslyInsertHtml（适用于外部HTML，优先使用）
        try {
          console.log('使用dangerouslyInsertHtml方法设置HTML内容');
          // 恢复选区
          editorRef.value.restoreSelection();
          editorRef.value.clear();
          // 使用setTimeout确保在下一个事件循环中执行，提高成功率
          setTimeout(() => {
            editorRef.value.dangerouslyInsertHtml(html);
            // 验证内容是否设置成功
            const newHtml = editorRef.value.getHtml();
            console.log('设置后的内容长度:', newHtml.length);
            console.log('设置后的内容预览:', newHtml.substring(0, 200) + '...');
          }, 0);
        } catch (insertError) {
          console.error('dangerouslyInsertHtml方法执行失败:', insertError);
          // 方法3：使用setHtml（适用于wangEditor生成的HTML）
          try {
            console.log('使用setHtml方法设置HTML内容');
            editorRef.value.setHtml(html);
            // 验证内容是否设置成功
            const newHtml = editorRef.value.getHtml();
            console.log('setHtml后的内容长度:', newHtml.length);
            console.log('setHtml后的内容预览:', newHtml.substring(0, 200) + '...');
          } catch (setHtmlError) {
            console.warn('富文本编辑器内容设置失败，依赖v-model更新:', setHtmlError);
          }
        }
      } else {
        console.log('内容相同，无需更新');
      }
    } catch (error) {
      console.error('富文本编辑器操作失败:', error);
    }
  } else {
    console.log('编辑器未初始化或已销毁');
  }
};

// 暴露方法
defineExpose({
  setHtmlContent,
  getEditor: () => editorRef.value,
});

// 处理内容变化
const handleChange = (editor: IDomEditor) => {
  const html = editor.getHtml();
  valueHtml.value = html;
  emit('update:modelValue', html);
  emit('change', html);
};

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor == null) return;
  editor.destroy();
});

// 计算编辑器高度
const editorHeight = computed(() => {
  if (typeof props.height === 'number') {
    return `${props.height}px`;
  }
  return props.height || '400px';
});
</script>

<template>
  <div class="rich-editor">
    <!-- 工具栏 -->
    <div class="flex items-center border-b border-gray-200">
      <Toolbar
        :editor="editorRef"
        :defaultConfig="toolbarConfig"
        :mode="'default'"
        class="flex-1"
      />
      <!-- 自定义iframe按钮 -->
      <Button
        type="text"
        size="small"
        @click="showIframeModal"
        class="mx-2"
        title="嵌入网页"
      >
        🌐 嵌入网页
      </Button>
    </div>

    <!-- 编辑器 -->
    <Editor
      v-model="valueHtml"
      :defaultConfig="editorConfig"
      :mode="'default'"
      :style="{ height: editorHeight }"
      class="border border-gray-200 border-t-0"
      @onCreated="handleCreated"
      @onChange="handleChange"
    />

    <!-- iframe插入弹窗 -->
    <Modal
      v-model:open="iframeModalVisible"
      title="嵌入外部网页"
      width="700px"
      @ok="insertIframe"
      @cancel="iframeModalVisible = false"
    >
      <Form layout="vertical" class="mt-4">
        <!-- 常用网站快速选择 -->
        <Form.Item label="快速选择常用网站">
          <div class="grid grid-cols-2 gap-2">
            <Button
              v-for="site in commonSites"
              :key="site.name"
              type="dashed"
              size="small"
              @click="selectCommonSite(site)"
              class="text-left"
            >
              {{ site.name }}
            </Button>
          </div>
        </Form.Item>

        <Form.Item label="网页链接" required>
          <Input
            v-model:value="iframeForm.url"
            placeholder="请输入要嵌入的网页链接，如：https://example.com"
            size="large"
          />
        </Form.Item>

        <Form.Item label="标题">
          <Input
            v-model:value="iframeForm.title"
            placeholder="可选：为嵌入的网页添加标题"
          />
        </Form.Item>

        <div class="grid grid-cols-2 gap-4">
          <Form.Item label="宽度">
            <Input
              v-model:value="iframeForm.width"
              placeholder="如：100%、800px"
            />
          </Form.Item>

          <Form.Item label="高度">
            <Input
              v-model:value="iframeForm.height"
              placeholder="如：400px、500px"
            />
          </Form.Item>
        </div>

        <div class="bg-blue-50 p-4 rounded-lg">
          <div class="text-sm text-blue-800">
            <p class="font-medium mb-2">💡 使用提示：</p>
            <ul class="list-disc list-inside space-y-1">
              <li>确保网页链接支持iframe嵌入</li>
              <li>某些网站（如微信、淘宝）可能禁止iframe嵌入</li>
              <li>建议使用HTTPS链接以确保安全性</li>
              <li>可以嵌入视频、地图、代码演示等内容</li>
            </ul>
          </div>
        </div>
      </Form>
    </Modal>
  </div>
</template>

<style scoped>
.rich-editor {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

.rich-editor:focus-within {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
</style>

<style>
/* 编辑器样式 */
.w-e-text-placeholder {
  color: #bfbfbf !important;
}

.w-e-text-container {
  background-color: #fff !important;
}

.w-e-text-container [data-slate-editor] {
  padding: 15px !important;
  line-height: 1.6 !important;
}

.w-e-toolbar {
  background-color: #fafafa !important;
  border-bottom: 1px solid #d9d9d9 !important;
}

.w-e-toolbar .w-e-bar-item button {
  color: #595959 !important;
}

.w-e-toolbar .w-e-bar-item button:hover {
  color: #1890ff !important;
  background-color: #e6f7ff !important;
}

.w-e-toolbar .w-e-bar-item.w-e-bar-item-active button {
  color: #1890ff !important;
  background-color: #e6f7ff !important;
}
</style>
