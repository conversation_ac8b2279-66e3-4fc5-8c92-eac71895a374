<iframe id="ueditor_1" width="100%" height="100%" frameborder="0"
  src="javascript:void(function(){document.open();document.write(&quot;&lt;!DOCTYPE html&gt;&lt;html lang='en'&gt;&lt;head&gt;&lt;meta name='referrer' content='never'&gt;&lt;meta charset='utf-8'&gt;&lt;meta name='viewport' content='width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no'&gt;&lt;style type='text/css'&gt;html.data_color_scheme_dark,html.data_color_scheme_dark body {background: #232323;color: rgba(255, 255, 255, 0.8);}html.data_color_scheme_dark ::-webkit-scrollbar-track {background-color: #2f2f2f;}html.data_color_scheme_dark ::-webkit-scrollbar-thumb {background-color: #666;-webkit-box-shadow: inset 0 0 16px #666;}.view{padding:0;word-wrap:break-word;cursor:text;}
body{margin:8px;font-family:sans-serif;font-size:16px;}p{margin:5px 0;}&lt;/style&gt;&lt;link rel='stylesheet' type='text/css' href='https://static.135editor.com/js/ueditor/themes/iframe.css?x=y06'/&gt;&lt;style id='initial-style'&gt;body{}&lt;/style&gt;&lt;/head&gt;&lt;body class='view' &gt;&lt;/body&gt;&lt;script type='text/javascript'  id='_initialScript'&gt;setTimeout(function(){editor = window.parent.UE.instants['ueditorInstant1'];editor._setup(document);},0);var _tmpScript = document.getElementById('_initialScript');_tmpScript.parentNode.removeChild(_tmpScript);&lt;/script&gt;&lt;/html&gt;&quot;);document.close();}())">



  <!DOCTYPE html>
  <html lang="zh-CN">

  <head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <meta content="ie=edge" http-equiv="X-UA-Compatible" />
    <title>中国税局紧盯跨境收入！Shopee等平台卖家避险指南2.0→</title>
    <meta content="别焦虑，信息收集≠强制征税。" name="description" />
    <meta content="果然" name="author" />
    <meta content="中国税局紧盯跨境收入！Shopee等平台卖家避险指南2.0→" property="og:title" />
    <meta content="https://mp.weixin.qq.com/s/lURLfH5wgbkE_yAlxXKXTw" property="og:url" />
    <meta
      content="https://mmbiz.qpic.cn/sz_mmbiz_jpg/tSpRwlNiakuQia20GMibASElBZ5VMyKVwTvFibj2cl4Khficic9rtDjzWzhDHEpX0GH1JLvV5wVqbIwSpg6vibCAYq7cQ/0?wx_fmt=jpeg"
      property="og:image" />
    <meta content="别焦虑，信息收集≠强制征税。" property="og:description" />
    <meta content="article" property="og:type" />
    <meta content="summary" property="twitter:card" />
    <meta
      content="https://mmbiz.qpic.cn/sz_mmbiz_jpg/tSpRwlNiakuQia20GMibASElBZ5VMyKVwTvFibj2cl4Khficic9rtDjzWzhDHEpX0GH1JLvV5wVqbIwSpg6vibCAYq7cQ/0?wx_fmt=jpeg"
      property="twitter:image" />
    <meta content="中国税局紧盯跨境收入！Shopee等平台卖家避险指南2.0→" property="twitter:title" />
    <meta content="别焦虑，信息收集≠强制征税。" property="twitter:description" />
    <style>
      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }

      html {
        font-size: 16px;
        line-height: 1.6;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        color: #333;
        background-color: #fff;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        word-wrap: break-word;
      }

      .article-header {
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 1px solid #eee;
      }

      .article-title {
        font-size: 28px;
        font-weight: bold;
        line-height: 1.3;
        color: #222;
        margin-bottom: 10px;
      }

      .article-content {
        font-size: 16px;
        line-height: 1.8;
      }

      .article-content p {
        margin: 15px 0;
      }

      .article-content h1,
      .article-content h2,
      .article-content h3,
      .article-content h4,
      .article-content h5,
      .article-content h6 {
        margin: 25px 0 15px 0;
        font-weight: bold;
        line-height: 1.3;
      }

      .article-content h1 {
        font-size: 24px;
      }

      .article-content h2 {
        font-size: 22px;
      }

      .article-content h3 {
        font-size: 20px;
      }

      .article-content h4 {
        font-size: 18px;
      }

      .article-content h5 {
        font-size: 16px;
      }

      .article-content h6 {
        font-size: 14px;
      }

      .article-content img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 20px auto;
        border-radius: 4px;
      }

      .article-content table {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
      }

      .article-content td,
      .article-content th {
        border: 1px solid #ddd;
        padding: 12px;
        text-align: left;
      }

      .article-content th {
        background-color: #f5f5f5;
        font-weight: bold;
      }

      .article-content ul,
      .article-content ol {
        margin: 15px 0;
        padding-left: 30px;
      }

      .article-content li {
        margin: 8px 0;
      }

      .article-content blockquote {
        margin: 20px 0;
        padding: 15px 20px;
        background-color: #f9f9f9;
        border-left: 4px solid #ddd;
        font-style: italic;
      }

      .article-content a {
        color: #1976d2;
        text-decoration: none;
      }

      .article-content a:hover {
        text-decoration: underline;
      }

      .article-content code {
        background-color: #f5f5f5;
        padding: 2px 6px;
        border-radius: 3px;
        font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
        font-size: 14px;
      }

      .article-content pre {
        background-color: #f6f8fa;
        border-radius: 6px;
        padding: 16px;
        overflow: auto;
        margin: 20px 0;
      }

      .article-content pre code {
        background: none;
        padding: 0;
      }

      @media (max-width: 768px) {
        body {
          padding: 15px;
        }

        .article-title {
          font-size: 24px;
        }

        .article-content {
          font-size: 15px;
        }
      }
    </style>
  </head>

  <body>
    <div class="article-header">
      <h1 class="article-title">中国税局紧盯跨境收入！Shopee等平台卖家避险指南2.0→</h1>
    </div>
    <div class="article-content">
      <div
        class="rich_media_content js_underline_content autoTypeSetting24psection wangeditor-content rich-text-content"
        id="js_content"
        style='visibility: visible; opacity: 1; font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; font-size: 16px; line-height: 1.6; color: #333; word-wrap: break-word; box-sizing: border-box'>
        <section data-pm-slice="0 0 []" style="margin: 0px">
          <section
            style="margin: 0px; padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; text-decoration-thickness: initial; font-size: 15px; font-family: mp-quote, system-ui, -apple-system, BlinkMacSystemFont, Arial, sans-serif">
            <section nodeleaf="" style="margin: 0px; padding: 0px; max-width: 100%; clear: both"><img alt="文章图片"
                src="https://mmbiz.qpic.cn/sz_mmbiz_gif/tSpRwlNiakuTibOZAB2Ka9YzgchzSLYxyb3RSFPIpJcw08hchibYAVv7s3pTUoVa8E494FUGV4qhlSNl6PaO4VVVA/640?wx_fmt=gif&amp;from=appmsg"
                style="margin: 0px; padding: 0px; vertical-align: baseline; color: var(--weui-FG-HALF); font-size: 17px; letter-spacing: 0.544px; height: auto !important; width: 100%; max-width: 100% !important; display: block; border-radius: 4px" />
            </section>
            <section style="margin: 0px; padding: 0px; max-width: 100%; letter-spacing: 0.544px">
              <section style="margin: 0px; padding: 0px; max-width: 100%; line-height: 1.8">
                <section style="margin: 0px; padding: 0px; max-width: 100%; line-height: 1.8">
                  <section
                    style="margin: 0px; padding: 0px; max-width: 100%; font-size: 17px; color: var(--weui-FG-HALF); letter-spacing: 0.544px">
                    <section
                      style="margin: 0px; padding: 0px; max-width: 100%; letter-spacing: 0.544px; color: rgb(160, 160, 160); line-height: 1.8">
                      <section class="js_uneditable custom_select_card mp_profile_iframe"
                        style="margin: 0px; padding: 0px; max-width: 100%; width: 100%">
                        <section nodeleaf="" style="margin: 0px auto; padding: 0px; max-width: 100%"><mp-common-profile
                            data-alias="DNY123dsgc" data-biz_account_status="0" data-from="2"
                            data-headimg="https://mmbiz.qlogo.cn/sz_mmbiz_png/tSpRwlNiakuSyMo4FKEje0zs3Wic3lOgFOFZX5Km7jnc3gVuHFiaBXyjDic6tW47AO80dsGnVibic6d7rHbibxmjicSfjA/300?wx_fmt=png"
                            data-id="MzkyMjUyMDM4OA==" data-index="0" data-is_biz_ban="0" data-nickname="东南亚电商观察"
                            data-origin_num="222" data-pluginname="mpprofile"
                            data-signature="直击东南亚，追踪Shopee/Lazada/TikTok/Temu等热点事件及输出深度观察。「打破出海信息差」是我们的slogan"></mp-common-profile>
                        </section>
                      </section>
                    </section>
                  </section>
                  <section class="custom_select_card mp_profile_iframe js_wx_tap_highlight"
                    style="margin: 0px 8px; padding: 0px; max-width: 100%; display: block; width: 559.43px; color: var(--weui-FG-HALF); letter-spacing: 0.544px; text-align: center">
                  </section>
                </section>
              </section>
            </section>
          </section>
          <section
            style="margin: 0px; padding: 0px; max-width: 100%; color: #3e3e3e; text-decoration-thickness: initial; font-size: 15px; text-align: right; letter-spacing: 0.544px; line-height: 1.5em; font-family: system-ui, -apple-system, Arial, sans-serif">
            <section
              style="margin: 0px; padding: 0px; max-width: 100%; letter-spacing: 0.544px; font-family: system-ui, -apple-system, BlinkMacSystemFont, Arial, sans-serif">
              <p
                style="text-align: right; margin: 0px 5px; padding: 0px; max-width: 100%; clear: both; line-height: 1.6; font-size: 16px">
                <span
                  style="color: #888888; font-size: 14px; letter-spacing: 0.5px; font-family: Arial, Helvetica, sans-serif"><span
                    leaf="">编辑｜果然</span></span><span leaf=""><br /></span></p>
            </section>
          </section>
          <section data-docx-has-block-data="true" data-lark-html-role="root" data-page-id="FO5GdEahOofWfQxNn6lcz6u3n9b"
            style="margin: 0px; padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; text-decoration-thickness: initial">
            <section data-docx-has-block-data="true" data-lark-html-role="root"
              data-page-id="FO5GdEahOofWfQxNn6lcz6u3n9b"
              style="margin: 0px; padding: 0px; max-width: 100%; color: #3e3e3e; letter-spacing: 0.578px; text-align: justify; background-color: #ffffff; text-decoration-thickness: initial">
              <section data-pm-slice="11 5 []" data-role="outer" style="margin: 0px">
                <section
                  style="text-align: right; letter-spacing: 0.544px; line-height: 1.5em; font-family: system-ui, -apple-system, Arial, sans-serif">
                  <p
                    style="text-align: right; margin-right: 5px; margin-left: 5px; margin: 10px 0; line-height: 1.6; font-size: 16px">
                    <span
                      style="letter-spacing: 0.5px; font-size: 14px; color: #888888; font-family: Arial, Helvetica, sans-serif"><span
                        style="color: #888888; font-size: 14px; letter-spacing: 0.5px"><span leaf=""> 投稿爆料/转载添加
                          | </span></span><span style="color: #888888; font-size: 14px; letter-spacing: 0.544px"><span
                          leaf="">DNY123-04</span></span></span></p>
                </section>
                <section style="">
                  <section data-docx-has-block-data="true" data-lark-html-role="root"
                    data-page-id="CJUhdF2KIoDCTBxEc44cUgU0nkh" style="">
                    <section data-pm-slice="0 0 []"
                      style="text-align: right; letter-spacing: 0.544px; line-height: 1.5em; font-family: system-ui, -apple-system, Arial, sans-serif">
                      <section style="margin: 10px auto">
                        <section style="background-color: #faf9f5; border-radius: 10px; margin: 0 5px; padding: 15px 0">
                          <section style="display: flex">
                            <section
                              style="font-size: 16px; color: #ffffff; text-align: center; background-color: #0c8918; border-radius: 5px 25px 25px 5px; padding: 3px 10px">
                              <strong data-brushtype="text"><span leaf="">文章速览：</span></strong></section>
                            <section style="width: 9px"><svg style="display: block" viewbox="0 0 14.34 44.55"
                                xmlns="http://www.w3.org/2000/svg">
                                <g>
                                  <g>
                                    <path
                                      d="M8.11,44.55Q0,35.58,0,22.41A33.83,33.83,0,0,1,8.11,0H14.3A36,36,0,0,0,6.61,22.41a35,35,0,0,0,7.73,22.14Z"
                                      style=""></path>
                                  </g>
                                </g>
                              </svg></section>
                          </section>
                          <section style="padding: 0 15px">
                            <section
                              style="background-color: #ffffff; padding: 10px; margin: 15px 0 0 0; border-radius: 10px">
                              <section data-autoskip="1"
                                style="text-align: justify; line-height: 1.75em; letter-spacing: 1.5px; font-size: 14px; color: #333333; background: transparent">
                                <p data-pm-slice="0 0 []" style="margin: 10px 0; line-height: 1.6; font-size: 16px">
                                  <span leaf="">卖家无须担心</span></p>
                                <p style="margin: 10px 0; line-height: 1.6; font-size: 16px"><span
                                    leaf="">常见问题答疑+直播解读预告</span></p>
                              </section>
                            </section>
                          </section>
                        </section>
                      </section>
                    </section><span
                      data-lark-record-data='{"rootId":"STKXdEfPXokUV1xCzdecR1JhnHh","text":{"initialAttributedTexts":{"text":{"0":"软件系统记录了从2月15日起的交易，总金额约为1200万元人民币（约430亿越南盾）。"},"attribs":{"0":"*0*1+17"}},"apool":{"numToAttrib":{"0":["author","7277739767822041092"],"1":["bold","true"]},"nextNum":2}},"type":"text","referenceRecordMap":{},"extra":{"channel":"saas","isEqualBlockSelection":false,"pasteRandomId":"ff99bda3-8a53-4ee6-9eeb-2aa58d497626","mention_page_title":{},"external_mention_url":{}},"isKeepQuoteContainer":false,"isFromCode":false,"selection":[{"id":17,"type":"text","selection":{"start":27,"end":70},"recordId":"OG8kd8iTgo4wBhxfpgIcd2Csnsd"}],"payloadMap":{},"isCut":false}'
                      data-lark-record-format="docx/text"></span>
                    <section style="margin-left: 8px; margin-right: 8px"><span leaf=""
                        style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">6月26日施行的《互联网平台企业涉税信息报送规定》：无论平台设在境内还是境外，只要为中国卖家提供服务，或向中国用户开展交易撮合活动，都必须依法报送涉税信息。简单说就是：<span
                          style="color: rgb(12, 101, 69); font-weight: bold"
                          textstyle="">平台要把卖家和从业人员的身份、收入等信息，每季度报送一次给税务局。</span></span></section>
                    <section style="margin-left: 8px; margin-right: 8px"><span leaf=""
                        style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                    </section>
                    <section style="margin-left: 8px; margin-right: 8px"><span leaf=""
                        style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><span
                          style="color: rgb(12, 101, 69); font-weight: normal" textstyle="">点击查看上一篇关于该规定的解读：</span><a
                          class="normal_text_link" data-itemshowtype="0" data-linktype="2"
                          href="https://mp.weixin.qq.com/s?__biz=MzkyMjUyMDM4OA==&amp;mid=2247491950&amp;idx=1&amp;sn=ad78843841f49a8a6db9fa48ac3d3272&amp;scene=21#wechat_redirect"
                          linktype="text" rel="noopener noreferrer" style="" target="_blank"
                          textvalue="Shopee等跨境平台须向中国税局报卖家收入：别慌！做好这些准备即可→">Shopee等跨境平台须向中国税局报卖家收入：别慌！做好这些准备即可→</a></span>
                    </section>
                    <section style="margin-left: 8px; margin-right: 8px"><span leaf=""
                        style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                    </section>
                    <section style="margin-left: 8px; margin-right: 8px"><span leaf=""
                        style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">消息一出，让不少卖家心里打鼓。其实不用慌，这份新规没那么“吓人”，咱们把问题搞清楚、做好准备就行。</span>
                    </section>
                    <section style="margin-left: 8px; margin-right: 8px"><span leaf=""
                        style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                    </section>
                    <section style="margin-left: 8px; margin-right: 8px"><span leaf=""
                        style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">本期文章我们将围绕<span
                          style="color: rgb(12, 101, 69); font-weight: bold" textstyle="">“卖家无需担心的原因”，“最关心的合规问题及避坑要点
                          ”，</span>来逐一解答。</span></section>
                    <section style="margin-left: 8px; margin-right: 8px"><span leaf=""
                        style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                    </section>
                    <section style="margin-left: 8px; margin-right: 8px"><span leaf=""
                        style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><span
                          style="text-decoration: none" textstyle="">（由于各大卖家对本次报税的相关内容还有很多疑问，我们特别邀请了</span><span
                          style="text-decoration: underline" textstyle="">税务合规专家钟俊老师</span><span
                          style="text-decoration: none" textstyle="">在明</span><span
                          style="font-weight: bold; text-decoration: none" textstyle="">天7月15日16：00-17:00</span><span
                          style="text-decoration: none" textstyle="">为大家进行直播解答，重点涵盖跨境电商平台上</span><span
                          style="font-weight: bold; text-decoration: none" textstyle="">中国公司主体的合规经营路径，以及</span><span
                          style="font-weight: bold; text-decoration: underline"
                          textstyle="">通过海外公司注册优化经营架构的实用方案，</span><span style="text-decoration: none"
                          textstyle="">助力卖家更清晰地规划跨境业务的合规发展方向。 )</span></span></section>
                    <section style="margin-left: 8px; margin-right: 8px"><span leaf=""
                        style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                    </section>
                    <section class="channels_iframe_wrp" nodeleaf=""><mp-common-videosnap
                        class="js_uneditable custom_select_card channels_live_iframe" data-desc="将在07月15日 16:00 直播"
                        data-headimgurl="https://wx.qlogo.cn/finderhead/1K86AOU628TldIaJhQOmB7TpamHSKiacAAvje6jw3nCMuXf1fu7sUBTaI2xZ3M4hnF1ictViaI7XicI/0"
                        data-intro="破解跨境平台报税迷雾" data-nickname="DNY123东南亚导航"
                        data-noticeid="finderlivenotice-v2_060000231003b20faec8c5e08910c4ddca02e934b0777463a01332fcb5dbd212b270fb0835da@finder-1752487648087524-543763972"
                        data-pluginname="mpvideosnap" data-status="0" data-type="live"
                        data-username="v2_060000231003b20faec8c5e08910c4ddca02e934b0777463a01332fcb5dbd212b270fb0835da@finder"></mp-common-videosnap>
                    </section>
                    <section style="margin-left: 8px; margin-right: 8px"><span leaf=""
                        style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                    </section>
                    <section nodeleaf=""
                      style="margin: 0px 8px; padding: 0px; max-width: 100%; color: rgb(62, 62, 62); font-size: 17px; font-style: normal; font-weight: 400; letter-spacing: 0.578px; text-indent: 0px; word-spacing: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; background-color: rgb(255, 255, 255); text-align: left; font-family: system-ui, -apple-system, BlinkMacSystemFont, Arial, sans-serif">
                      <img alt="文章图片"
                        src="https://mmbiz.qpic.cn/sz_mmbiz_png/tSpRwlNiakuSCcJrX6hbjWhOSFTSbcheMeC716LQX0GAicTZHibkjgJgKlK1UdTbEJkeOz5yCC0tXHzQjGD5Qiawsw/640?wx_fmt=png&amp;from=appmsg&amp;&amp;wxfrom=5&amp;wx_lazy=1&amp;wx_co=1&amp;tp=webp"
                        style="margin: 0px; padding: 0px; max-width: 100%; vertical-align: baseline; height: auto !important; width: 661px !important; display: block; border-radius: 4px" />
                    </section>
                    <section
                      style="margin: 0px 8px; padding: 0px; max-width: 100%; color: rgb(62, 62, 62); font-size: 17px; font-style: normal; font-weight: 400; letter-spacing: 0.578px; text-align: justify; text-indent: 0px; word-spacing: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; background-color: rgb(255, 255, 255); font-family: system-ui, -apple-system, BlinkMacSystemFont, Arial, sans-serif">
                      <p
                        style="margin: 0px 8px; padding: 0px; max-width: 100%; clear: both; line-height: 1.6; font-size: 16px">
                        <strong
                          style="margin: 0px; padding: 0px; max-width: 100%; font-size: 18px; line-height: 1.56em; font-family: Arial, Helvetica, sans-serif"><span
                            style="margin: 0px; padding: 0px; max-width: 100%"><span
                              style="margin: 0px; padding: 0px; font-size: 16px; letter-spacing: 0.578px; font-family: Arial, Helvetica, sans-serif; max-width: 100%"><span
                                leaf=""
                                style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><span
                                  style="font-size: 17px; color: rgb(0, 0, 0); font-weight: bold"
                                  textstyle="">为什么说，这个政策卖家无需过度担心？</span></span></span></span></strong></p>
                    </section>
                    <section style="margin-left: 8px; margin-right: 8px"><span leaf=""
                        style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                    </section>
                    <section style="margin-left: 8px; margin-right: 8px"><span leaf=""
                        style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">卖家最慌的点：“流水计税”
                        会压垮生意？</span></section>
                    <section style="margin-left: 8px; margin-right: 8px"><span leaf=""
                        style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                    </section>
                    <section style="margin-left: 8px; margin-right: 8px"><span leaf=""
                        style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">举个真实例子：</span>
                    </section>
                    <section style="margin-left: 8px; margin-right: 8px"><span leaf=""
                        style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">月流水
                        100 万的店铺，进货成本 70 万 + 平台佣金 15 万 + 仓储人工 10 万 ，最后利润可能连 5 万都不到。</span></section>
                    <section style="margin-left: 8px; margin-right: 8px"><span leaf=""
                        style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                    </section>
                    <section style="margin-left: 8px; margin-right: 8px"><span leaf=""
                        style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">要是税务部门直接按
                        100 万流水收税，大部分卖家根本扛不住，这就是焦虑的根源。</span></section>
                    <section style="margin-left: 8px; margin-right: 8px"><span leaf=""
                        style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                    </section>
                    <section style="margin-left: 8px; margin-right: 8px"><span leaf=""
                        style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">不过，</span><span
                        data-pm-slice="0 0 []"
                        style='color: rgb(64, 64, 64); font-family: quote-cjk-patch, Inter, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Noto Sans", Ubuntu, Cantarell, "Helvetica Neue", Oxygen, "Open Sans", sans-serif; font-size: 16.002px; font-style: normal; font-weight: 400; letter-spacing: normal; text-align: start; text-indent: 0px; word-spacing: 0px; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; display: inline !important; float: none'><span
                          leaf="">这种担忧其实存在几个关键误区。</span></span></section>
                    <section style="margin-left: 8px; margin-right: 8px"><span leaf=""
                        style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                    </section>
                    <section style="margin-left: 8px; margin-right: 8px"><span data-pm-slice="0 0 []"
                        style="color: rgb(62, 62, 62); font-family: Arial, Helvetica, sans-serif; font-size: 16px; font-style: normal; font-weight: 400; letter-spacing: 0.578px; text-align: justify; text-indent: 0px; word-spacing: 0px; background-color: rgb(255, 249, 234); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; display: inline !important; float: none"><span
                          leaf="">👉 </span></span><span leaf=""
                        style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><span
                          style="color: rgb(12, 101, 69); font-weight: bold"
                          textstyle="">政策核心是“信息收集”，不是“强制征税”：</span>此次监管采取 “先了解情况、再逐步治理” 的思路，并非
                        “平台一上报信息，税务部门就立即上门核查”。</span></section>
                    <section style="margin-left: 8px; margin-right: 8px"><span leaf=""
                        style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                    </section>
                    <section style="margin-left: 8px; margin-right: 8px"><span data-pm-slice="0 0 []"
                        style="color: rgb(62, 62, 62); font-family: Arial, Helvetica, sans-serif; font-size: 16px; font-style: normal; font-weight: 400; letter-spacing: 0.578px; text-align: justify; text-indent: 0px; word-spacing: 0px; background-color: rgb(255, 249, 234); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; display: inline !important; float: none"><span
                          leaf="">👉 </span></span><span leaf=""
                        style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><span
                          style="color: rgb(12, 101, 69); font-weight: bold" textstyle="">多数小店其实能免税：</span>例如，个体户月销售额不超过
                        10 万元的，免征增值税；年收入不超过 12 万元的，也有个人所得税减免。所以大部分小店、小主播以及兼职从业者的收入，符合免税条件的依旧能享受免税优惠。</span></section>
                    <section style="margin-left: 8px; margin-right: 8px"><span leaf=""
                        style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                    </section>
                    <section style="margin-left: 8px; margin-right: 8px"><span data-pm-slice="0 0 []"
                        style="color: rgb(62, 62, 62); font-family: Arial, Helvetica, sans-serif; font-size: 16px; font-style: normal; font-weight: 400; letter-spacing: 0.578px; text-align: justify; text-indent: 0px; word-spacing: 0px; background-color: rgb(255, 249, 234); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; display: inline !important; float: none"><span
                          leaf="">👉<span style="color: rgb(12, 101, 69); font-weight: bold"
                            textstyle=""></span></span></span><span leaf=""
                        style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><span
                          style="color: rgb(12, 101, 69); font-weight: bold"
                          textstyle="">交税看利润，不是流水：</span>纳税金额的多少，关键取决于利润而非流水。在电商行业，大家都清楚这样的情况：100
                        万元的销售额，扣除平台服务费、广告费、仓储费、人力成本、推广费用以及开票加点等支出后，实际利润可能仅有几万元，甚至还会出现亏损。</span></section>
                    <section style="margin-left: 8px; margin-right: 8px"><span leaf=""
                        style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                    </section>
                    <section style="margin-left: 8px; margin-right: 8px"><span leaf=""
                        style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">税务部门征收利润相关税费时，也需先明确是否存在利润。因此不必过度担心，<span
                          style="font-weight: bold" textstyle="">并非销售越多，纳税就一定越多。</span></span></section>
                    <section style="margin-left: 8px; margin-right: 8px"><span leaf=""
                        style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                    </section>
                    <section style="margin-left: 8px; margin-right: 8px"><span leaf=""
                        style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">最后还有一点需要说明，此次新规引用的政策基础是
                        2001 年的《税收征管法》，当时淘宝尚未上线，电商行业也未兴起。这表明<span style="font-weight: bold" textstyle="">整个监管体系正处于
                          “跟进完善” 的阶段，尚未完全实现更新升级。</span></span></section><mp-common-profile data-alias="DNY123dsgc"
                      data-from="2"
                      data-headimg="https://mmbiz.qlogo.cn/sz_mmbiz_png/tSpRwlNiakuSyMo4FKEje0zs3Wic3lOgFOFZX5Km7jnc3gVuHFiaBXyjDic6tW47AO80dsGnVibic6d7rHbibxmjicSfjA/0?wx_fmt=png"
                      data-id="MzkyMjUyMDM4OA==" data-is_biz_ban="0" data-nickname="东南亚电商观察" data-pluginname="mpprofile"
                      data-service_type="1"
                      data-signature="直击东南亚，追踪Shopee/Lazada/TikTok/Temu等热点事件及输出深度观察。「打破出海信息差」是我们的slogan"
                      data-verify_status="0"></mp-common-profile>
                    <section style="margin: 0px">
                      <section nodeleaf=""
                        style="margin: 0px 8px; padding: 0px; max-width: 100%; color: rgb(62, 62, 62); font-size: 17px; font-style: normal; font-weight: 400; letter-spacing: 0.578px; text-indent: 0px; word-spacing: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; background-color: rgb(255, 255, 255); text-align: left; font-family: system-ui, -apple-system, BlinkMacSystemFont, Arial, sans-serif">
                        <img alt="文章图片"
                          src="https://mmbiz.qpic.cn/sz_mmbiz_png/tSpRwlNiakuSCcJrX6hbjWhOSFTSbcheMUibRfZZNG5SpibTC6MiabrIrHlq2vK9bhCBmCaqibO3nUnuDBkGFGyqgPA/640?wx_fmt=png&amp;from=appmsg&amp;&amp;wxfrom=5&amp;wx_lazy=1&amp;wx_co=1&amp;tp=webp"
                          style="margin: 0px; padding: 0px; max-width: 100%; vertical-align: baseline; height: auto !important; width: 661px !important; display: block; border-radius: 4px" />
                      </section>
                      <section
                        style="margin: 0px; padding: 0px; max-width: 100%; color: rgb(62, 62, 62); font-size: 17px; font-style: normal; font-weight: 400; letter-spacing: 0.578px; text-align: justify; text-indent: 0px; word-spacing: 0px; white-space: normal; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; background-color: rgb(255, 255, 255); font-family: system-ui, -apple-system, BlinkMacSystemFont, Arial, sans-serif">
                        <section style="margin: 0px 8px; padding: 0px; max-width: 100%; text-align: left"><strong
                            style="margin: 0px; padding: 0px; max-width: 100%; text-align: justify; font-size: 18px; line-height: 1.56em; font-family: Arial, Helvetica, sans-serif"><span
                              style="margin: 0px; padding: 0px; max-width: 100%"><span leaf=""
                                style="margin: 0px; padding: 0px; max-width: 100%"> 卖家最关心的几个问题</span></span></strong>
                        </section>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><span
                            style="font-weight: bold" textstyle="">（1）会不会查过去的账？</span></span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">新规严格遵循“法不溯及既往”原则，仅要求互联网平台企业报送新规实施后的经营数据，无需追溯此前的涉税信息。但这并不意味着过往涉税问题可以“豁免”——若存在明显的偷税、逃税等违法行为，税务部门依据《中华人民共和国税收征收管理法》等法律法规开展正常执法调查，属于法定职责范围内的监管行为，并非针对既往数据的“倒查”。因此，对于历史遗留的涉税问题，建议相关主体及时自查补正，切勿抱有侥幸心理。</span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><span
                            style="font-weight: bold" textstyle="">（2）为啥会被税务预警？</span></span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">税务部门将实施
                          “双比对” 机制：营业执照申报的销售额与平台报送税务部门的销售数据进行比对。若两者差异过大，可能引发税务关注。例如，自行申报月销 5 万元但平台数据显示为 20
                          万元，此类情况可能触发税务预警。</span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><span
                            style="font-weight: bold" textstyle="">（3）现在注销账号能“躲”过去吗？</span></span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">当前账号注销操作仍可执行，但一旦触发税务预警，后续注销将无法规避相关责任。注销并非
                          “安全退出”，注销过程中仍需按规定提交所需资料。相较于通过注销逃避问题，建议及时梳理账目，推动经营结构合规化。</span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><span
                            style="font-weight: bold" textstyle="">（4）现在合规还来得及吗？</span></span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">完全来得及！</span><span
                          data-pm-slice="0 0 []"
                          style='color: rgba(0, 0, 0, 0.85); font-family: Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", "SF Pro SC", "SF Pro Display", "SF Pro Icons", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: 16px; font-style: normal; font-weight: 400; letter-spacing: normal; text-align: start; text-indent: 0px; word-spacing: 0px; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; display: inline !important; float: none'><span
                            leaf="">需重点处理 7-9 月的数据，因 10
                            月平台首次收入报送将以此为依据。此前未完成申报的应及时补报，收入较高的主体可优化经营结构，未上报的隐性收入需尽快筹划合规。</span></span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">新规目前只是"数据收集"，关键看后续执行细则。理清三本账，<span
                            style="font-weight: bold" textstyle="">平台流水（卖多少）成本支出（花多少），税务申报（报多少），</span><span
                            style="color: rgb(12, 101, 69); font-weight: bold"
                            textstyle="">三者口径必须一致，</span>避免数据矛盾引发风险。</span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">新规不可怕，慌的是
                          “稀里糊涂经营”！</span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span data-pm-slice="0 0 []"
                          style="color: rgb(62, 62, 62); font-family: Arial, Helvetica, sans-serif; font-size: 16px; font-style: normal; font-weight: 400; letter-spacing: 0.578px; text-align: justify; text-indent: 0px; word-spacing: 0px; background-color: rgb(255, 249, 234); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; display: inline !important; float: none"><span
                            leaf="">👉</span></span><span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><span
                            style="color: rgb(12, 101, 69); font-weight: bold" textstyle="">别踩这些坑</span></span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">别信“包过”“保合规”的噱头，税务合规没有捷径；</span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">警惕“0税率”“免税妙招”，这些大多是陷阱，后期被稽查更麻烦；</span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">一定要规范费用报销，<span
                            style="font-weight: bold" textstyle="">保留好进货单、发票等凭证。</span></span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><span
                            style="font-size: 20px; color: rgb(12, 101, 69); font-weight: bold"
                            textstyle="">常见问题：</span></span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><span
                            style="font-weight: bold" textstyle="">1、</span><a class="normal_text_link"
                            data-itemshowtype="0" data-linktype="2"
                            href="https://mp.weixin.qq.com/s?__biz=MzkyMjUyMDM4OA==&amp;mid=2247491950&amp;idx=1&amp;sn=ad78843841f49a8a6db9fa48ac3d3272&amp;scene=21#wechat_redirect"
                            linktype="text" rel="noopener noreferrer" style="" target="_blank"
                            textvalue="平台具体会报哪些数据？"><span style="font-weight: bold"
                              textstyle="">平台具体会报哪些数据？</span></a></span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">平台需按季度报送两类核心信息：一是<span
                            style="font-weight: bold" textstyle="">身份信息，</span>包括企业 / 个人名称、统一社会信用代码或身份证号、店铺 ID
                          及联系方式；<span style="font-weight: bold"
                            textstyle="">二是收入信息，</span>涵盖销售总额（未扣佣金）、退款额、净收入、订单量，同时需单独上报平台服务费。</span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><span
                            style="font-weight: bold" textstyle="">2、小卖家流水不大，也要被上报吗？</span></span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">除境外卖家向单一中国买家季度净额≤5,000
                          元的交易可暂不报送外，其余情况无规模豁免。国内主体开店、使用第三方收款工具的卖家均被纳入报送范围。</span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><span
                            style="font-weight: bold" textstyle="">3、平台报的数据包含佣金、退款吗？</span></span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">公告明确要求上报
                          “收入总额（未扣佣金）”“退款金额”“收入净额”，且平台服务费需单独报送。卖家可通过 “总额 — 退款” 核对自家账目，再扣除佣金等成本后进行纳税申报。</span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><span
                            style="font-weight: bold" textstyle="">4、交多少税，关键看流水还是利润？</span></span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">关键看利润而非流水。销售额扣除平台服务费、广告费、仓储费、人力成本等支出后的利润才是计税基础。即便流水高，若利润低甚至亏损，纳税金额也会相应减少。</span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><span
                            style="font-weight: bold" textstyle="">5、很多中小卖家其实可以免税吗？</span></span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">是的。例如个体户月销售额不超
                          10 万可免增值税，年收入不超 12 万有个税减免，大部分小店、小主播、兼职收入的免税政策仍继续适用。</span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><span
                            style="font-weight: bold" textstyle="">6、如果平台或卖家迟报 / 瞒报，会怎样？</span></span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">平台首次迟报
                          / 瞒报将被罚款 2-10 万元，屡犯或情节严重的会被停业整顿，并处 10-50 万元罚款。卖家若漏报收入，被稽查后需补税、缴纳滞纳金，还可能被加收 50%-500%
                          的罚款。</span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><span
                            style="font-weight: bold" textstyle="">7、发现平台把我的数据报错了，能改吗？</span></span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">可以。公告允许
                          “更正报送”，平台需在发现错误起 30 日内向主管税局提交修正表。卖家应第一时间反馈平台客服或招商经理，并保留书面邮件 / 工单以备税局抽查。</span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><span
                            style="font-weight: bold" textstyle="">8、 第三方收款或支付机构也可能被调取数据？</span></span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">是的。税局在稽查时若需要更详细的合同、交易明细、资金账户、物流信息，可直接要求平台及第三方支付机构协助提供，这些机构不得以
                          “权限不足” 等理由拒绝。</span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><span
                            style="font-weight: bold" textstyle="">9、代运营、SaaS、MCN 会受波及吗？</span></span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">会。公告将
                          “提供商家入驻、店铺运营、营销推广等服务” 的公司列为可被指定的报送主体。代运营公司若掌握卖家流水，需按要求报送，卖家仍需对自身数据真实性负责。</span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><span
                            style="font-weight: bold" textstyle="">10、人在海外、店在海外，只卖给中国买家也要上报吗？</span></span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">取决于交易金额。若对单一中国买家季度销售额净额＞5,000
                          元人民币，平台必须报送该笔收入；≤5,000 元可暂免。只要对中国市场有可观销售，就需纳入监管。</span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><span
                            style="font-weight: bold" textstyle="">11、人在海外，店的主体也是海外公司，卖给海外买家需要上报吗？</span></span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">无需上报。但需注意，若作为中国公民，相关销售款项转入个人境内账户，需依据个人所得税相关规定履行纳税义务，这与海外公司的纳税义务分属不同范畴，应分别规范处理。</span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><span
                            style="font-weight: bold" textstyle="">12、平台上报数据后，税局会马上来核查吗？</span></span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">不会。政策核心在
                          “信息收集” 而非 “强制征税”，此次是 “先了解、再治理”，并非 “平台一上报，税局就来敲门”，监管过程会循序渐进。</span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><span
                            style="font-weight: bold" textstyle="">13、季度中间开店，当季数据如何报送？</span></span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">平台会按店铺实际经营时段统计收入数据，卖家在当季开店后产生的销售额需纳入当季报送范围，开店前的收入无需补报。</span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><span
                            style="font-weight: bold" textstyle="">14、免税政策需要主动申请吗？</span></span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">符合免税条件（如个体户月销售额不超
                          10 万、年收入不超 12 万等）的卖家，一般无需主动申请，但需确保自行申报时准确填写相关信息，以便享受减免优惠。</span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">若您对跨境经营中的<span
                            style="font-weight: bold" textstyle="">税务合规、主体架构搭建</span>等问题还有疑问，欢迎关注明日直播<span
                            style="font-size: 16px; color: rgb(12, 101, 69); font-weight: bold"
                            textstyle="">【</span></span><span leaf=""><span
                            style="font-size: 16px; color: rgb(12, 101, 69); font-weight: bold"
                            textstyle="">破解跨境平台报税迷雾：海外主体架构合规指南</span></span><span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><span
                            style="font-size: 16px; color: rgb(12, 101, 69); font-weight: bold"
                            textstyle="">】</span>。</span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">本次直播特别邀请到<span
                            style="color: rgb(12, 101, 69); font-weight: bold"
                            textstyle="">税务合规专家——安合出海联合创始人钟俊老师</span>分享专业见解，重点涵盖跨境电商平台上中国公司主体的合规经营路径，以及通过海外公司注册优化经营架构的实用方案，助力卖家更清晰地规划跨境业务的合规发展方向。<span
                            style="font-weight: bold" textstyle="">点击下方预约直播↓</span></span></section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                      </section>
                      <section class="channels_iframe_wrp" nodeleaf=""><mp-common-videosnap
                          class="js_uneditable custom_select_card channels_live_iframe" data-desc="将在07月15日 16:00 直播"
                          data-headimgurl="https://wx.qlogo.cn/finderhead/1K86AOU628TldIaJhQOmB7TpamHSKiacAAvje6jw3nCMuXf1fu7sUBTaI2xZ3M4hnF1ictViaI7XicI/0"
                          data-intro="破解跨境平台报税迷雾" data-nickname="DNY123东南亚导航"
                          data-noticeid="finderlivenotice-v2_060000231003b20faec8c5e08910c4ddca02e934b0777463a01332fcb5dbd212b270fb0835da@finder-1752487648087524-543763972"
                          data-pluginname="mpvideosnap" data-status="0" data-type="live"
                          data-username="v2_060000231003b20faec8c5e08910c4ddca02e934b0777463a01332fcb5dbd212b270fb0835da@finder"></mp-common-videosnap>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                      </section>
                      <section nodeleaf="" style="text-align: center"><img alt="文章图片"
                          src="https://mmbiz.qpic.cn/sz_mmbiz_jpg/tSpRwlNiakuQia20GMibASElBZ5VMyKVwTvGnbQhprOsLpHO52Ps7VicssibQr4AYYic5krSSiavc5Qo26zkzHyZhQ0aw/640?wx_fmt=jpeg&amp;from=appmsg"
                          style="width: 100%; max-width: 100%; height: auto; display: block; margin: 10px auto; border-radius: 4px" />
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)">需要了解/咨询行业相关信息的卖家也可以扫描下方二维码联系我们与各大卖家交流分享。</span>
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                      </section>
                      <section nodeleaf="" style="text-align: center"><img alt="文章图片"
                          src="https://mmbiz.qpic.cn/sz_mmbiz_jpg/tSpRwlNiakuQia20GMibASElBZ5VMyKVwTvUYx1fftAjHicNDtaJhzibt8DuVMnqf8FQPibVibymyJibxgrO425n0W0r8g/640?wx_fmt=jpeg&amp;from=appmsg"
                          style="width: 200px; height: 200px; max-width: 100%; display: block; margin: 10px auto; border-radius: 4px" />
                      </section>
                      <section
                        style="padding: 0px; max-width: 100%; color: rgb(62, 62, 62); letter-spacing: 0.578px; text-align: justify; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; margin: 0px 8px">
                        <span leaf=""
                          style="color: rgb(62, 62, 62); font-size: 16px; font-family: Arial, Helvetica, sans-serif; letter-spacing: 0.578px; background-color: rgb(255, 255, 255)"><br /></span>
                        <section style="margin: 0px; padding: 0px; max-width: 100%">
                          <section data-docx-has-block-data="true" data-lark-html-role="root"
                            data-page-id="C96UdteogoNOeox1Hxrc4Cmonag"
                            style="margin: 0px; padding: 0px; max-width: 100%">
                            <section data-docx-has-block-data="true" data-lark-html-role="root"
                              data-page-id="QxYidzeCToSDMqx4ZTfcFD4Bnre"
                              style="margin: 0px; padding: 0px; max-width: 100%">
                              <section style="margin: 0px; padding: 0px; max-width: 100%">
                                <section style="margin: 0px; padding: 0px; max-width: 100%">
                                  <p
                                    style="text-align: center; margin: 0px; padding: 0px; max-width: 100%; clear: both; line-height: 1.6; font-size: 16px">
                                    <span leaf=""><img alt="文章图片"
                                        src="https://mmbiz.qpic.cn/sz_mmbiz_png/tSpRwlNiakuSC5dG2JPcHDUNlyfE4sXXSxBuO5EIvD1kVmFe2LicG1icVz04p4MuVWpcpuMrLgib1u3iayfl5I3YRrw/640?wx_fmt=png&amp;from=appmsg"
                                        style="vertical-align: baseline; width: 389px; height: 121px; max-width: 100%; display: block; margin: 10px auto; border-radius: 4px" /></span>
                                  </p>
                                </section>
                                <section
                                  style="margin: 0px; padding: 0px; max-width: 100%; text-align: center; height: 0px">
                                </section>
                              </section>
                              <section style="margin: 0px; padding: 0px; max-width: 100%"><span
                                  style="margin: 0px; padding: 0px; max-width: 100%; font-size: 16px; font-family: Arial, Helvetica, sans-serif"></span>
                              </section>
                            </section>
                            <section style="margin: 0px; padding: 0px; max-width: 100%; height: 0px"></section>
                          </section>
                        </section>
                      </section>
                      <section data-role="paragraph" style="margin: 0px; padding: 0px; max-width: 100%">
                        <section style="margin: 0px; padding: 0px; max-width: 100%">
                          <section
                            style="margin: 0px; padding: 0px; max-width: 100%; font-size: 17px; font-family: system-ui, -apple-system, BlinkMacSystemFont, Arial, sans-serif">
                            <section data-docx-has-block-data="true" data-page-id="PStCdXI3BoABWcxLt0fcaLQmnUh"
                              style="margin: 0px; padding: 0px; max-width: 100%; letter-spacing: 0.544px">
                              <section
                                style="margin: 0px; padding: 0px; max-width: 100%; letter-spacing: 0.544px; font-size: 12px; clear: both; height: 0px">
                              </section>
                            </section>
                            <section style="margin: 0px; padding: 0px; max-width: 100%; letter-spacing: 0.544px">
                              <section style="margin: 0px; padding: 0px; max-width: 100%">
                                <section
                                  style="margin: 0px; padding: 0px; max-width: 100%; color: rgba(0, 0, 0, 0.9); font-size: 17px">
                                  <section style="margin: 0px; padding: 0px; max-width: 100%; letter-spacing: 0.544px">
                                    <section style="margin: 0px; padding: 0px; max-width: 100%; text-align: center">
                                      <section style="margin: 0px; padding: 0px; max-width: 100%; line-height: 1.8">
                                        <section
                                          style="margin: 0px; padding: 0px; max-width: 100%; color: rgba(0, 0, 0, 0.9); font-size: 17px; letter-spacing: 0.544px; font-family: mp-quote, -apple-system-font, BlinkMacSystemFont, Arial, sans-serif">
                                          <section
                                            style="margin: 0px; padding: 0px; max-width: 100%; text-align: center">
                                            <section nodeleaf=""
                                              style="margin: 0px 5px; padding: 0px; max-width: 100%; clear: both"><img
                                                alt="文章图片"
                                                src="https://mmbiz.qpic.cn/sz_mmbiz_gif/tSpRwlNiakuSLxoCLKQtUU1ewGBkUlNbWFr5LGA75q1h29CjHFkh0IBhiaQiaibwiaczzymXRvWibfx2G9JHUQ4dyDdQ/640?wx_fmt=gif&amp;from=appmsg"
                                                style="margin: 0px; padding: 0px; vertical-align: baseline; height: auto !important; width: 100%; max-width: 100% !important; display: block; border-radius: 4px" />
                                            </section>
                                            <section data-pm-slice="0 0 []"
                                              style="margin: 0px; padding: 0px; max-width: 100%; text-align: center">
                                              <section nodeleaf=""
                                                style="margin: 0px 5px; padding: 0px; max-width: 100%; clear: both"><img
                                                  alt="文章图片"
                                                  src="https://mmbiz.qpic.cn/sz_mmbiz_png/tSpRwlNiakuQDr1Y8ly3yNlVR4nc68jBiatQS5u337A2ULZMmVG3CToNBVRTabOMjkcKnOXBuicOBrEStXV8OIYiaA/640?wx_fmt=png&amp;from=appmsg"
                                                  style="margin: 0px; padding: 0px; vertical-align: baseline; height: auto !important; width: 100%; max-width: 100%; display: block; border-radius: 4px" />
                                              </section>
                                              <section nodeleaf=""
                                                style="margin: 0px 5px; padding: 0px; max-width: 100%; clear: both"><img
                                                  alt="文章图片"
                                                  src="https://mmbiz.qpic.cn/sz_mmbiz_png/tSpRwlNiakuQDr1Y8ly3yNlVR4nc68jBiaYfuJtvfPcerRJLfmsUJusSs4M39yzkibfgB1zTxv5MpEKuiaC5Yp4LsA/640?wx_fmt=png&amp;from=appmsg"
                                                  style="margin: 0px; padding: 0px; vertical-align: baseline; color: #3e3e3e; font-size: 17px; letter-spacing: 0.544px; text-align: center; background-color: #ffffff; text-decoration-thickness: initial; height: auto !important; width: 100%; font-family: mp-quote, -apple-system-font, BlinkMacSystemFont, Arial, sans-serif; max-width: 100%; display: block; border-radius: 4px" />
                                              </section>
                                            </section>
                                            <section style="margin: 0px; padding: 0px; max-width: 100%">
                                              <section
                                                style="margin: 0px; padding: 0px; max-width: 100%; text-align: center">
                                                <section nodeleaf=""
                                                  style="margin: 0px 5px; padding: 0px; max-width: 100%; clear: both">
                                                  <img alt="文章图片"
                                                    src="https://mmbiz.qpic.cn/sz_mmbiz_jpg/tSpRwlNiakuRibcBuOce5GKNmyyDm7zJwxiaw7GJeJB3lCE9Qu1QEAxZsBwJMHuJ0p6sSN7gs5mDoWv7wsN3gaY8g/640?wx_fmt=jpeg&amp;from=appmsg"
                                                    style="margin: 0px; padding: 0px; vertical-align: baseline; border-radius: 9px; height: auto !important; width: 100%; max-width: 100%; display: block" />
                                                </section>
                                                <p
                                                  style="margin: 0px 8px; padding: 0px; max-width: 100%; clear: both; letter-spacing: 0.544px; line-height: 1.6; font-size: 16px">
                                                  <span style="margin: 0px; padding: 0px; max-width: 100%"><span
                                                      leaf=""><img alt="文章图片"
                                                        src="https://mmbiz.qpic.cn/sz_mmbiz_png/tSpRwlNiakuS8ZQzHL9zyrXWR6rK5sLAiaeOh4Wr6v0fka9eqCEdO074eDBcDMt77ofI0y7IcLQDzTmasicEdh87Q/640?wx_fmt=png&amp;from=appmsg"
                                                        style="margin: 0px; padding: 0px; vertical-align: baseline; height: auto !important; width: 661px !important; max-width: 100%; display: block; border-radius: 4px" /><img
                                                        alt="文章图片"
                                                        src="https://mmbiz.qpic.cn/sz_mmbiz_png/tSpRwlNiakuQDr1Y8ly3yNlVR4nc68jBiaKGF4z1tzW5MVmOJVHiblSicvSTKcZsR4DP5fXUlZ6d1icwyRqRbsNgO5g/640?wx_fmt=png&amp;from=appmsg"
                                                        style="margin: 0px; padding: 0px; vertical-align: baseline; height: auto !important; width: 661px !important; max-width: 100%; display: block; border-radius: 4px" /></span></span>
                                                </p>
                                                <section nodeleaf=""
                                                  style="margin: 0px 8px; padding: 0px; max-width: 100%; clear: both; letter-spacing: 0.544px">
                                                  <img alt="文章图片"
                                                    src="https://mmbiz.qpic.cn/sz_mmbiz_png/tSpRwlNiakuQDr1Y8ly3yNlVR4nc68jBiaRz3IMp4UF0Y5EwSW1z4omWPtSLASn0NfvlCck0Zp3CHEuYkniaVnYDw/640?wx_fmt=png&amp;from=appmsg"
                                                    style="margin: 0px; padding: 0px; vertical-align: baseline; color: rgba(0, 0, 0, 0.9); letter-spacing: 0.544px; height: auto !important; width: 100%; max-width: 100%; display: block; border-radius: 4px" />
                                                </section>
                                                <section nodeleaf=""
                                                  style="text-align: center; margin-left: 8px; margin-right: 8px"><img
                                                    alt="文章图片"
                                                    src="https://mmbiz.qpic.cn/sz_mmbiz_png/tSpRwlNiakuQia20GMibASElBZ5VMyKVwTvTOtREjlWOxvbY4Ief1kviakK0tAO1AzRfdgy4TXub1aYc08TupXEmSg/640?wx_fmt=png&amp;from=appmsg"
                                                    style="max-width: 100%; height: auto; display: block; margin: 10px auto; border-radius: 4px" />
                                                </section>
                                              </section>
                                            </section>
                                          </section>
                                        </section>
                                      </section>
                                    </section>
                                  </section>
                                </section>
                              </section>
                            </section>
                          </section>
                        </section>
                      </section>
                      <section data-id="150590" data-tools="135编辑器" style="margin: 0px; padding: 0px; max-width: 100%">
                        <section style="margin: 10px auto; padding: 0px; max-width: 100%">
                          <section data-op="division" style="margin: 0px; padding: 0px; max-width: 100%; display: flex">
                            <span leaf=""><br /></span>
                            <section data-width="33%" nodeleaf=""
                              style="margin: 0px; padding: 0px; width: 33%; max-width: 33% !important"><img alt="文章图片"
                                src="https://mmbiz.qpic.cn/sz_mmbiz_jpg/tSpRwlNiakuSCcJrX6hbjWhOSFTSbcheMU5qmtNeXhScSHWgsz1iakSE8ttxILnBX2sbpsulZAlN1TrvMkgcjwSw/640?wx_fmt=jpeg&amp;from=appmsg"
                                style="margin: 0px; padding: 0px; vertical-align: baseline; display: block; border-radius: 9px; height: auto !important; width: 100%; max-width: 100% !important" />
                            </section>
                            <section data-width="33%" nodeleaf=""
                              style="margin: 0px; padding: 0px; width: 33%; max-width: 33% !important"><img alt="文章图片"
                                src="https://mmbiz.qpic.cn/sz_mmbiz_jpg/tSpRwlNiakuSCcJrX6hbjWhOSFTSbcheM2flL4gpYn81hSdyWgpaVMTZlJIrmIO4eKjeCBv7JLJCibIeQxPWhCIA/640?wx_fmt=jpeg&amp;from=appmsg"
                                style="margin: 0px; padding: 0px; vertical-align: baseline; display: block; border-radius: 6px; height: auto !important; width: 100%; max-width: 100% !important" />
                            </section>
                            <section data-width="33%" nodeleaf=""
                              style="margin: 0px; padding: 0px; width: 33%; max-width: 33% !important"><img alt="文章图片"
                                src="https://mmbiz.qpic.cn/sz_mmbiz_jpg/tSpRwlNiakuSCcJrX6hbjWhOSFTSbcheMrVGfBOD0fuIaaEY2qT5K5h3QDnMgzFKWkfVIdbYvPvJrOYoxduWlgA/640?wx_fmt=jpeg&amp;from=appmsg"
                                style="margin: 0px; padding: 0px; vertical-align: baseline; display: block; border-radius: 9px; height: auto !important; width: 100%; max-width: 100% !important" />
                            </section>
                          </section>
                        </section>
                      </section>
                      <section nodeleaf=""
                        style="margin: 0px 5px; padding: 0px; max-width: 100%; clear: both; font-size: 12px; letter-spacing: 0.544px; font-family: system-ui, -apple-system, BlinkMacSystemFont, Arial, sans-serif">
                        <img alt="文章图片"
                          src="https://mmbiz.qpic.cn/sz_mmbiz_gif/tSpRwlNiakuSCcJrX6hbjWhOSFTSbcheMWqt3hJ9vVZyr4IDsuqHWgR1picYYpzicz0r1CUgI8ddewrATwuMla3LQ/640?wx_fmt=gif&amp;from=appmsg"
                          style="margin: 0px; padding: 0px; vertical-align: baseline; letter-spacing: 0.578px; text-align: center; height: auto !important; width: 100%; max-width: 100% !important; display: block; border-radius: 4px" />
                      </section>
                    </section>
                  </section>
                </section>
                <p
                  style="text-align: left; margin-right: 5px; margin-bottom: 0px; margin-left: 5px; font-size: 12px; letter-spacing: 0.544px; font-family: system-ui, -apple-system, BlinkMacSystemFont, Arial, sans-serif; margin: 10px 0; line-height: 1.6">
                  <span style="color: #888888"><span style="font-family: 黑体, SimHei"><span
                        leaf="">*信息来源：国家税务总局关于互联网平台企业报送涉税信息有关事项的公告；《互联网平台企业涉税信息报送规定》；小红书等综合网站</span></span></span></p>
                <p
                  style="text-align: left; margin-right: 5px; margin-bottom: 0px; margin-left: 5px; font-size: 12px; letter-spacing: 0.544px; font-family: system-ui, -apple-system, BlinkMacSystemFont, Arial, sans-serif; margin: 10px 0; line-height: 1.6">
                  <span style="color: #888888"><span style="font-family: 黑体, SimHei"><span
                        leaf="">*转载请与编辑取得联系，</span><span style="font-family: 黑体, SimHei"><span
                          leaf="">未经许可同意，</span></span><span leaf="">违规转载、复制必究！</span></span></span></p>
              </section>
            </section>
          </section>
        </section>
      </div>
    </div>
  </body>

  </html>




</iframe>
