<iframe id="ueditor_1" width="100%" height="100%" frameborder="0"
  src="javascript:void(function(){document.open();document.write(&quot;&lt;!DOCTYPE html&gt;&lt;html lang='en'&gt;&lt;head&gt;&lt;meta name='referrer' content='never'&gt;&lt;meta charset='utf-8'&gt;&lt;meta name='viewport' content='width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no'&gt;&lt;style type='text/css'&gt;html.data_color_scheme_dark,html.data_color_scheme_dark body {background: #232323;color: rgba(255, 255, 255, 0.8);}html.data_color_scheme_dark ::-webkit-scrollbar-track {background-color: #2f2f2f;}html.data_color_scheme_dark ::-webkit-scrollbar-thumb {background-color: #666;-webkit-box-shadow: inset 0 0 16px #666;}.view{padding:0;word-wrap:break-word;cursor:text;}
body{margin:8px;font-family:sans-serif;font-size:16px;}p{margin:5px 0;}&lt;/style&gt;&lt;link rel='stylesheet' type='text/css' href='https://static.135editor.com/js/ueditor/themes/iframe.css?x=y06'/&gt;&lt;style id='initial-style'&gt;body{}&lt;/style&gt;&lt;/head&gt;&lt;body class='view' &gt;&lt;/body&gt;&lt;script type='text/javascript'  id='_initialScript'&gt;setTimeout(function(){editor = window.parent.UE.instants['ueditorInstant1'];editor._setup(document);},0);var _tmpScript = document.getElementById('_initialScript');_tmpScript.parentNode.removeChild(_tmpScript);&lt;/script&gt;&lt;/html&gt;&quot;);document.close();}())"></iframe>
