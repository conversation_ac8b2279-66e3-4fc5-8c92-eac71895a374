# 按钮设计规范

## 设计原则

根据项目需求，所有按钮都应该采用简洁的设计风格，**不添加图标**，只使用文字标签。

## 按钮规范

### 1. 基本按钮样式

#### ✅ 推荐写法
```vue
<!-- 主要按钮 -->
<Button type="primary">
  确认
</Button>

<!-- 次要按钮 -->
<Button type="default">
  取消
</Button>

<!-- 危险按钮 -->
<Button type="primary" danger>
  删除
</Button>

<!-- 链接按钮 -->
<Button type="link">
  查看详情
</Button>

<!-- 虚线按钮 -->
<Button type="dashed">
  添加项目
</Button>
```

#### ❌ 避免使用
```vue
<!-- 不要添加图标 -->
<Button type="primary">
  <PlusIcon :size="16" />
  添加
</Button>

<!-- 不要使用复杂的图标组合 -->
<Button type="primary">
  <template #icon>
    <SaveIcon />
  </template>
  保存
</Button>
```

### 2. 按钮尺寸

```vue
<!-- 大尺寸 - 用于重要操作 -->
<Button type="primary" size="large">
  提交表单
</Button>

<!-- 默认尺寸 - 常规操作 -->
<Button type="primary">
  确认
</Button>

<!-- 小尺寸 - 表格操作等 -->
<Button type="text" size="small">
  编辑
</Button>
```

### 3. 按钮状态

```vue
<!-- 加载状态 -->
<Button type="primary" :loading="loading">
  {{ loading ? '提交中...' : '提交' }}
</Button>

<!-- 禁用状态 -->
<Button type="primary" :disabled="!isValid">
  提交
</Button>
```

### 4. 按钮组合

```vue
<!-- 操作按钮组 -->
<Space>
  <Button type="default">
    取消
  </Button>
  <Button type="primary">
    确认
  </Button>
</Space>

<!-- 表格操作按钮 -->
<Space size="small">
  <Button type="text" size="small">
    编辑
  </Button>
  <Button type="text" size="small" danger>
    删除
  </Button>
</Space>
```

## 文字标签规范

### 1. 动作类按钮
- **确认** - 确认操作
- **取消** - 取消操作
- **提交** - 提交表单
- **保存** - 保存数据
- **删除** - 删除操作
- **编辑** - 编辑操作
- **添加** - 添加新项
- **搜索** - 搜索功能
- **重置** - 重置表单
- **刷新** - 刷新数据

### 2. 状态类按钮
- **登录中...** - 登录加载状态
- **提交中...** - 提交加载状态
- **保存中...** - 保存加载状态
- **删除中...** - 删除加载状态
- **搜索中...** - 搜索加载状态

### 3. 导航类按钮
- **返回** - 返回上一页
- **下一步** - 流程下一步
- **上一步** - 流程上一步
- **完成** - 完成流程
- **查看详情** - 查看详细信息

## 实际应用示例

### 1. 表单按钮
```vue
<template>
  <Form>
    <!-- 表单内容 -->
    
    <!-- 表单操作按钮 -->
    <div class="text-center mt-6">
      <Space>
        <Button size="large" @click="handleCancel">
          取消
        </Button>
        <Button 
          type="primary" 
          size="large" 
          :loading="loading"
          @click="handleSubmit"
        >
          {{ loading ? '提交中...' : '提交' }}
        </Button>
      </Space>
    </div>
  </Form>
</template>
```

### 2. 表格操作按钮
```vue
<template>
  <Table>
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'action'">
        <Space size="small">
          <Button 
            type="text" 
            size="small"
            @click="handleEdit(record)"
          >
            编辑
          </Button>
          <Button 
            type="text" 
            size="small" 
            danger
            @click="handleDelete(record)"
          >
            删除
          </Button>
        </Space>
      </template>
    </template>
  </Table>
</template>
```

### 3. 搜索表单按钮
```vue
<template>
  <Form layout="inline">
    <!-- 搜索字段 -->
    
    <Form.Item>
      <Space>
        <Button @click="handleReset">
          重置
        </Button>
        <Button 
          type="primary" 
          :loading="searching"
          @click="handleSearch"
        >
          {{ searching ? '搜索中...' : '搜索' }}
        </Button>
      </Space>
    </Form.Item>
  </Form>
</template>
```

### 4. 弹窗按钮
```vue
<template>
  <Modal>
    <template #footer>
      <Space>
        <Button @click="handleCancel">
          取消
        </Button>
        <Button 
          type="primary" 
          :loading="loading"
          @click="handleConfirm"
        >
          {{ loading ? '处理中...' : '确认' }}
        </Button>
      </Space>
    </template>
  </Modal>
</template>
```

## 颜色和样式指南

### 1. 按钮类型选择
- **type="primary"** - 主要操作（提交、确认、保存等）
- **type="default"** - 次要操作（取消、重置等）
- **type="text"** - 表格内操作、链接式操作
- **type="link"** - 导航链接
- **type="dashed"** - 添加类操作
- **danger** - 危险操作（删除等）

### 2. 尺寸选择
- **size="large"** - 重要表单提交按钮
- **默认尺寸** - 常规操作按钮
- **size="small"** - 表格内操作按钮

## 注意事项

1. **保持一致性**：整个项目中的按钮样式应该保持一致
2. **语义化文字**：按钮文字应该清晰表达操作意图
3. **状态反馈**：重要操作应该有加载状态反馈
4. **无障碍性**：确保按钮有合适的对比度和可点击区域
5. **响应式**：在不同屏幕尺寸下按钮应该正常显示

## 迁移指南

如果现有代码中有使用图标的按钮，请按以下方式修改：

### 修改前
```vue
<Button type="primary">
  <PlusIcon :size="16" />
  添加用户
</Button>
```

### 修改后
```vue
<Button type="primary">
  添加用户
</Button>
```

### 批量修改建议
1. 搜索项目中所有包含图标的按钮
2. 移除图标相关代码
3. 确保按钮文字清晰表达操作意图
4. 测试按钮功能是否正常

通过遵循这些规范，可以确保整个项目的按钮设计保持一致性和简洁性。
