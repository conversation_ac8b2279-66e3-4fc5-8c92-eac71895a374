<script setup lang="ts">
import { ref, computed } from 'vue';
import {
  Modal,
  Form,
  Input,
  Button,
  Space,
  Alert,
  Spin,
  Tabs,
  message
} from 'ant-design-vue';


// 导入store
import { useWechatCrawlerStore } from '#/store/article/wechat-crawler';
import type { WechatArticleInfo } from '#/store/article/wechat-crawler';

defineOptions({
  name: 'WechatCrawlerModal',
});

const props = defineProps<{
  visible: boolean;
}>();

const emit = defineEmits<{
  'update:visible': [visible: boolean];
  'crawl-success': [data: WechatArticleInfo];
}>();

// 使用store
const crawlerStore = useWechatCrawlerStore();

// 表单数据
const formData = ref({
  url: '',
  pastedContent: '',
  title: '',
});

// 当前标签页
const activeTab = ref('url');

// 表单引用
const formRef = ref();

// 表单验证规则（简化版）
const formRules = {
  url: [
    { required: true, message: '请输入微信文章链接', trigger: 'blur' }
  ],
};

// 计算属性
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

// 示例链接
const exampleUrls = [
  'https://mp.weixin.qq.com/s/xxxxxxxxxx',
  'https://mp.weixin.qq.com/s?__biz=xxx&mid=xxx&idx=1&sn=xxx',
];

// 处理爬取
async function handleCrawl() {
  try {
    // 先验证表单
    await formRef.value?.validate();

    // 显示加载提示
    const loadingMessage = message.loading('正在验证链接并爬取文章内容...', 0);

    try {
      // 调用爬取方法（内部会先验证URL）
      const articleData = await crawlerStore.crawlArticle(formData.value.url);

      loadingMessage();
      message.success('文章爬取成功！');
      emit('crawl-success', articleData);
      handleCancel();
    } catch (error: any) {
      loadingMessage();
      throw error;
    }
  } catch (error: any) {
    console.error('爬取失败:', error);

    // 根据错误类型显示不同的提示
    if (error.message?.includes('无效的微信公众号文章链接')) {
      message.error('链接格式不正确，请检查是否为有效的微信公众号文章链接');
    } else if (error.message?.includes('服务暂时不可用')) {
      message.error('爬取服务暂时不可用，请稍后重试或使用手动粘贴功能');
    } else {
      message.error(error.message || '文章爬取失败，请检查链接是否正确');
    }
  }
}

// 处理粘贴内容
async function handlePastedContent() {
  try {
    if (!formData.value.pastedContent.trim()) {
      message.error('请输入或粘贴文章内容');
      return;
    }

    const articleData = crawlerStore.parseUserPastedContent(
      formData.value.pastedContent,
      formData.value.title
    );

    message.success('内容解析成功！');
    emit('crawl-success', articleData);
    handleCancel();
  } catch (error: any) {
    console.error('解析粘贴内容失败:', error);
    message.error(error.message || '内容解析失败');
  }
}

// 从剪贴板获取内容
async function handleGetClipboard() {
  try {
    const clipboardContent = await crawlerStore.getClipboardContent();
    if (clipboardContent) {
      formData.value.pastedContent = clipboardContent;
      message.success('已从剪贴板获取内容');
    } else {
      message.warning('剪贴板内容为空');
    }
  } catch (error: any) {
    console.error('获取剪贴板失败:', error);
    message.error(error.message || '获取剪贴板内容失败');
  }
}

// 处理取消
function handleCancel() {
  modalVisible.value = false;
  formData.value = {
    url: '',
    pastedContent: '',
    title: '',
  };
  activeTab.value = 'url';
  formRef.value?.resetFields();
}

// 填入示例链接
function fillExampleUrl(url: string) {
  formData.value.url = url;
}

// 处理URL输入变化
function handleUrlChange() {
  // URL变化时的处理逻辑
  console.log('URL输入变化:', formData.value.url);
}

// 处理URL实时输入
function handleUrlInput() {
  // 实时输入时的处理逻辑
  console.log('URL实时输入:', formData.value.url);
}
</script>

<template>
  <Modal
    v-model:open="modalVisible"
    title="微信文章爬取"
    width="600px"
    :confirm-loading="crawlerStore.loading"
    @cancel="handleCancel"
  >
    <div class="wechat-crawler-modal">
      <!-- 使用说明 -->
      <Alert
        message="微信文章导入"
        description="支持两种方式导入微信文章：1) 输入文章链接自动爬取；2) 手动粘贴文章内容。"
        type="info"
        show-icon
        class="mb-4"
      />

      <!-- 标签页 -->
      <Tabs v-model:activeKey="activeTab" class="mb-4">
        <!-- URL爬取标签页 -->
        <Tabs.TabPane key="url" tab="🔗 链接爬取">
          <Form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            layout="vertical"
          >
            <Form.Item label="微信文章链接" name="url">
              <div class="url-input-wrapper">
                <Input
                  v-model:value="formData.url"
                  placeholder="请输入微信公众号文章链接，如：https://mp.weixin.qq.com/s/..."
                  size="large"
                  allow-clear
                  @input="handleUrlInput"
                  @change="handleUrlChange"
                />
                <Button
                  v-if="formData.url && crawlerStore.isValidWechatUrl(formData.url)"
                  type="primary"
                  size="small"
                  :loading="crawlerStore.loading || crawlerStore.validating"
                  :disabled="crawlerStore.validating"
                  class="crawl-btn"
                  @click="handleCrawl"
                >
                  {{ crawlerStore.validating ? '验证中' : '爬取' }}
                </Button>
              </div>

              <!-- URL状态提示 -->
              <div v-if="formData.url" class="text-xs mt-2">
                <div v-if="crawlerStore.validating" class="text-blue-600">
                  🔍 正在验证链接...
                </div>
                <div v-else-if="crawlerStore.isValidWechatUrl(formData.url)" class="text-green-600">
                  ✅ 链接格式正确，点击爬取按钮获取内容
                </div>
                <div v-else class="text-red-600">
                  ❌ 请输入有效的微信公众号文章链接
                </div>
              </div>
            </Form.Item>
          </Form>

          <!-- 示例链接 -->
          <div class="mb-4">
            <div class="text-sm text-gray-600 mb-2">链接格式示例：</div>
            <div class="space-y-1">
              <div
                v-for="(url, index) in exampleUrls"
                :key="index"
                class="text-xs text-gray-500 bg-gray-50 p-2 rounded cursor-pointer hover:bg-gray-100"
                @click="fillExampleUrl(url)"
              >
                {{ url }}
              </div>
            </div>
          </div>
        </Tabs.TabPane>

        <!-- 手动粘贴标签页 -->
        <Tabs.TabPane key="paste" tab="📋 手动粘贴">
          <Form layout="vertical">
            <Form.Item label="文章标题（可选）">
              <Input
                v-model:value="formData.title"
                placeholder="请输入文章标题"
                size="large"
              />
            </Form.Item>

            <Form.Item label="文章内容">
              <div class="flex gap-2 mb-2">
                <Button
                  type="dashed"
                  size="small"
                  @click="handleGetClipboard"
                >
                  从剪贴板获取
                </Button>
              </div>
              <Input.TextArea
                v-model:value="formData.pastedContent"
                placeholder="请粘贴微信文章内容（支持富文本格式）"
                :rows="8"
                show-count
                :maxlength="50000"
              />
            </Form.Item>
          </Form>

          <!-- 使用提示 -->
          <Alert
            message="使用提示"
            type="info"
            show-icon
            class="mb-4"
          >
            <template #description>
              <ol class="text-sm space-y-1">
                <li>1. 在微信文章页面选中全部内容（Ctrl+A）</li>
                <li>2. 复制内容（Ctrl+C）</li>
                <li>3. 点击"从剪贴板获取"或直接粘贴到文本框</li>
                <li>4. 点击"解析内容"完成导入</li>
              </ol>
            </template>
          </Alert>
        </Tabs.TabPane>
      </Tabs>

      <!-- 注意事项 -->
      <Alert
        message="注意事项"
        type="warning"
        show-icon
        class="mb-4"
      >
        <template #description>
          <ul class="text-sm space-y-1">
            <li>• 仅支持微信公众号文章链接（mp.weixin.qq.com）</li>
            <li>• 由于跨域限制，链接爬取可能失败，建议使用手动粘贴</li>
            <li>• 爬取的内容会自动清理格式，保留基本样式</li>
            <li>• 请遵守相关法律法规，仅爬取有权限的内容</li>
          </ul>
        </template>
      </Alert>

      <!-- 加载状态 -->
      <div v-if="crawlerStore.loading || crawlerStore.validating" class="text-center py-4">
        <Spin size="large" />
        <div class="mt-2 text-gray-600">
          <div v-if="crawlerStore.validating">正在验证链接有效性...</div>
          <div v-else-if="crawlerStore.loading && activeTab === 'url'">正在爬取文章内容，请稍候...</div>
          <div v-else-if="crawlerStore.loading && activeTab === 'paste'">正在解析内容，请稍候...</div>
        </div>
      </div>
    </div>

    <template #footer>
      <Space>
        <Button @click="handleCancel">取消</Button>

        <!-- URL爬取按钮 -->
        <Button
          v-if="activeTab === 'url'"
          type="primary"
          :loading="crawlerStore.loading || crawlerStore.validating"
          :disabled="!formData.url || !crawlerStore.isValidWechatUrl(formData.url) || crawlerStore.validating"
          @click="handleCrawl"
        >
          {{ crawlerStore.loading ? '爬取中...' : crawlerStore.validating ? '验证中...' : '开始爬取' }}
        </Button>

        <!-- 内容解析按钮 -->
        <Button
          v-if="activeTab === 'paste'"
          type="primary"
          :loading="crawlerStore.loading"
          :disabled="!formData.pastedContent.trim()"
          @click="handlePastedContent"
        >
          解析内容
        </Button>
      </Space>
    </template>
  </Modal>
</template>

<style scoped>
.wechat-crawler-modal {
  max-height: 60vh;
  overflow-y: auto;
}

.example-url {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  background: #f5f5f5;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  cursor: pointer;
  transition: all 0.2s;
}

.example-url:hover {
  background: #e6f7ff;
  border-color: #91d5ff;
}

:deep(.ant-alert-description ul) {
  margin: 0;
  padding-left: 0;
  list-style: none;
}

:deep(.ant-input-affix-wrapper-lg) {
  padding: 8px 12px;
}

.url-input-wrapper {
  display: flex;
  gap: 8px;
  align-items: flex-start;
}

.url-input-wrapper .ant-input {
  flex: 1;
}

.crawl-btn {
  flex-shrink: 0;
  height: 40px;
}
</style>
